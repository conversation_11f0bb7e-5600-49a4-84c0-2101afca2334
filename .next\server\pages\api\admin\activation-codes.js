"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/activation-codes";
exports.ids = ["pages/api/admin/activation-codes"];
exports.modules = {

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "uuid":
/*!***********************!*\
  !*** external "uuid" ***!
  \***********************/
/***/ ((module) => {

module.exports = import("uuid");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./lib/activation-codes.js":
/*!*********************************!*\
  !*** ./lib/activation-codes.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"deleteActivationCode\": () => (/* binding */ deleteActivationCode),\n/* harmony export */   \"disableActivationCode\": () => (/* binding */ disableActivationCode),\n/* harmony export */   \"enableActivationCode\": () => (/* binding */ enableActivationCode),\n/* harmony export */   \"generateActivationCode\": () => (/* binding */ generateActivationCode),\n/* harmony export */   \"getActivationCodeStats\": () => (/* binding */ getActivationCodeStats),\n/* harmony export */   \"getAllActivationCodes\": () => (/* binding */ getAllActivationCodes),\n/* harmony export */   \"getAllUsers\": () => (/* binding */ getAllUsers),\n/* harmony export */   \"useActivationCode\": () => (/* binding */ useActivationCode),\n/* harmony export */   \"validateActivationCode\": () => (/* binding */ validateActivationCode)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"uuid\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([uuid__WEBPACK_IMPORTED_MODULE_2__]);\nuuid__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// lib/activation-codes.js\n\n\n\nconst CODES_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), \"data\", \"activation-codes.json\");\n// 确保数据目录存在\nfunction ensureDataDir() {\n    const dataDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(CODES_FILE);\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(dataDir)) {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(dataDir, {\n            recursive: true\n        });\n    }\n}\n// 读取激活码数据\nfunction readCodesData() {\n    ensureDataDir();\n    if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(CODES_FILE)) {\n        return [];\n    }\n    try {\n        const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(CODES_FILE, \"utf8\");\n        return JSON.parse(data);\n    } catch (error) {\n        console.error(\"Error reading activation codes:\", error);\n        return [];\n    }\n}\n// 写入激活码数据\nfunction writeCodesData(codes) {\n    ensureDataDir();\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(CODES_FILE, JSON.stringify(codes, null, 2));\n        return true;\n    } catch (error) {\n        console.error(\"Error writing activation codes:\", error);\n        return false;\n    }\n}\n// 生成激活码\nfunction generateActivationCode(description = \"\", expiresInDays = 30, maxUsageCount = 1) {\n    const codes = readCodesData();\n    const code = (0,uuid__WEBPACK_IMPORTED_MODULE_2__.v4)().replace(/-/g, \"\").substring(0, 16).toUpperCase();\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + expiresInDays);\n    const newCode = {\n        id: (0,uuid__WEBPACK_IMPORTED_MODULE_2__.v4)(),\n        code,\n        description,\n        createdAt: new Date().toISOString(),\n        expiresAt: expiresAt.toISOString(),\n        maxUsageCount: Math.max(1, parseInt(maxUsageCount) || 1),\n        usedCount: 0,\n        isUsed: false,\n        usedAt: null,\n        usedBy: null,\n        usedByList: [],\n        isActive: true\n    };\n    codes.push(newCode);\n    if (writeCodesData(codes)) {\n        return newCode;\n    }\n    return null;\n}\n// 验证激活码\nfunction validateActivationCode(code) {\n    const codes = readCodesData();\n    const activationCode = codes.find((c)=>c.code === code.toUpperCase());\n    if (!activationCode) {\n        return {\n            valid: false,\n            error: \"INVALID_CODE\"\n        };\n    }\n    if (!activationCode.isActive) {\n        return {\n            valid: false,\n            error: \"DISABLED_CODE\"\n        };\n    }\n    // 检查使用次数限制\n    const usedCount = activationCode.usedCount || 0;\n    const maxUsageCount = activationCode.maxUsageCount || 1;\n    if (usedCount >= maxUsageCount) {\n        return {\n            valid: false,\n            error: \"USAGE_LIMIT_EXCEEDED\"\n        };\n    }\n    // 保持向后兼容性检查\n    if (activationCode.isUsed && maxUsageCount === 1) {\n        return {\n            valid: false,\n            error: \"USED_CODE\"\n        };\n    }\n    if (new Date() > new Date(activationCode.expiresAt)) {\n        return {\n            valid: false,\n            error: \"EXPIRED_CODE\"\n        };\n    }\n    return {\n        valid: true,\n        code: activationCode\n    };\n}\n// 使用激活码\nfunction useActivationCode(code, userEmail) {\n    const codes = readCodesData();\n    const codeIndex = codes.findIndex((c)=>c.code === code.toUpperCase());\n    if (codeIndex === -1) {\n        return false;\n    }\n    const validation = validateActivationCode(code);\n    if (!validation.valid) {\n        return false;\n    }\n    const activationCode = codes[codeIndex];\n    // 增加使用次数\n    activationCode.usedCount = (activationCode.usedCount || 0) + 1;\n    // 更新使用者列表\n    if (!activationCode.usedByList) {\n        activationCode.usedByList = [];\n    }\n    activationCode.usedByList.push({\n        email: userEmail,\n        usedAt: new Date().toISOString()\n    });\n    // 更新最后使用信息（保持向后兼容）\n    activationCode.usedAt = new Date().toISOString();\n    activationCode.usedBy = userEmail;\n    // 如果达到最大使用次数，标记为已使用（保持向后兼容）\n    const maxUsageCount = activationCode.maxUsageCount || 1;\n    if (activationCode.usedCount >= maxUsageCount) {\n        activationCode.isUsed = true;\n    }\n    return writeCodesData(codes);\n}\n// 获取所有激活码\nfunction getAllActivationCodes() {\n    return readCodesData();\n}\n// 禁用激活码\nfunction disableActivationCode(codeId) {\n    const codes = readCodesData();\n    const codeIndex = codes.findIndex((c)=>c.id === codeId);\n    if (codeIndex === -1) {\n        return false;\n    }\n    codes[codeIndex].isActive = false;\n    return writeCodesData(codes);\n}\n// 启用激活码\nfunction enableActivationCode(codeId) {\n    const codes = readCodesData();\n    const codeIndex = codes.findIndex((c)=>c.id === codeId);\n    if (codeIndex === -1) {\n        return false;\n    }\n    codes[codeIndex].isActive = true;\n    return writeCodesData(codes);\n}\n// 删除激活码\nfunction deleteActivationCode(codeId) {\n    const codes = readCodesData();\n    const filteredCodes = codes.filter((c)=>c.id !== codeId);\n    if (filteredCodes.length === codes.length) {\n        return false // 没有找到要删除的代码\n        ;\n    }\n    return writeCodesData(filteredCodes);\n}\n// 获取所有使用者列表\nfunction getAllUsers() {\n    const codes = readCodesData();\n    const users = [];\n    codes.forEach((code)=>{\n        if (code.usedByList && code.usedByList.length > 0) {\n            code.usedByList.forEach((usage)=>{\n                users.push({\n                    email: usage.email,\n                    usedAt: usage.usedAt,\n                    activationCode: code.code,\n                    codeDescription: code.description || \"无描述\",\n                    codeId: code.id,\n                    maxUsageCount: code.maxUsageCount || 1,\n                    currentUsedCount: code.usedCount || 0\n                });\n            });\n        }\n    });\n    // 按使用时间倒序排列\n    users.sort((a, b)=>new Date(b.usedAt) - new Date(a.usedAt));\n    return users;\n}\n// 获取激活码统计信息\nfunction getActivationCodeStats() {\n    const codes = readCodesData();\n    const total = codes.length;\n    const active = codes.filter((c)=>c.isActive).length;\n    const expired = codes.filter((c)=>new Date() > new Date(c.expiresAt)).length;\n    // 重新计算使用状态，考虑使用次数\n    const used = codes.filter((c)=>{\n        const usedCount = c.usedCount || 0;\n        const maxUsageCount = c.maxUsageCount || 1;\n        return usedCount >= maxUsageCount;\n    }).length;\n    const available = codes.filter((c)=>{\n        const usedCount = c.usedCount || 0;\n        const maxUsageCount = c.maxUsageCount || 1;\n        return c.isActive && usedCount < maxUsageCount && new Date() <= new Date(c.expiresAt);\n    }).length;\n    // 新增统计：多次使用激活码相关\n    const multiUse = codes.filter((c)=>(c.maxUsageCount || 1) > 1).length;\n    const totalUsages = codes.reduce((sum, c)=>sum + (c.usedCount || 0), 0);\n    const partiallyUsed = codes.filter((c)=>{\n        const usedCount = c.usedCount || 0;\n        const maxUsageCount = c.maxUsageCount || 1;\n        return usedCount > 0 && usedCount < maxUsageCount;\n    }).length;\n    return {\n        total,\n        active,\n        used,\n        expired,\n        available,\n        multiUse,\n        totalUsages,\n        partiallyUsed\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/activation-codes.js\n");

/***/ }),

/***/ "(api)/./pages/api/admin/activation-codes.js":
/*!*********************************************!*\
  !*** ./pages/api/admin/activation-codes.js ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"config\": () => (/* binding */ config),\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/activation-codes */ \"(api)/./lib/activation-codes.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// pages/api/admin/activation-codes.js\n\n\nconst SESSION_SECRET = process.env.SESSION_SECRET || \"your-secret-key\";\nconst ADMIN_EMAIL = \"<EMAIL>\";\n// 配置API路由\nconst config = {\n    api: {\n        bodyParser: {\n            sizeLimit: \"1mb\"\n        }\n    }\n};\nfunction verifyAdmin(req) {\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_1___default().parse(req.headers.cookie || \"\");\n    const sessionToken = cookies.adminSession;\n    const adminEmail = cookies.adminEmail;\n    if (!sessionToken || !adminEmail) {\n        return false;\n    }\n    // 验证邮箱是否为管理员邮箱\n    if (adminEmail !== ADMIN_EMAIL) {\n        return false;\n    }\n    try {\n        const decoded = Buffer.from(sessionToken, \"base64\").toString();\n        const [user, timestamp, secret] = decoded.split(\":\");\n        if (user !== \"admin\" || secret !== SESSION_SECRET) {\n            return false;\n        }\n        // 检查session是否过期（24小时）\n        const sessionTime = parseInt(timestamp);\n        const now = Date.now();\n        const maxAge = 24 * 60 * 60 * 1000 // 24小时\n        ;\n        if (now - sessionTime > maxAge) {\n            return false;\n        }\n        return true;\n    } catch (error) {\n        return false;\n    }\n}\nasync function handler(req, res) {\n    // 验证管理员权限\n    if (!verifyAdmin(req)) {\n        return res.status(401).json({\n            error: \"Unauthorized\"\n        });\n    }\n    const { method  } = req;\n    switch(method){\n        case \"GET\":\n            // 获取所有激活码和统计信息\n            try {\n                const codes = (0,_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__.getAllActivationCodes)();\n                const stats = (0,_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__.getActivationCodeStats)();\n                res.status(200).json({\n                    codes,\n                    stats\n                });\n            } catch (error) {\n                res.status(500).json({\n                    error: \"Failed to fetch activation codes\"\n                });\n            }\n            break;\n        case \"POST\":\n            // 生成新的激活码\n            try {\n                const { description , expiresInDays , maxUsageCount  } = req.body;\n                const newCode = (0,_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__.generateActivationCode)(description, expiresInDays, maxUsageCount);\n                if (newCode) {\n                    res.status(201).json({\n                        success: true,\n                        code: newCode\n                    });\n                } else {\n                    res.status(500).json({\n                        error: \"Failed to generate activation code\"\n                    });\n                }\n            } catch (error) {\n                res.status(500).json({\n                    error: \"Failed to generate activation code\"\n                });\n            }\n            break;\n        case \"PUT\":\n            // 更新激活码状态（启用/禁用）\n            try {\n                const { codeId , action  } = req.body;\n                let success = false;\n                if (action === \"disable\") {\n                    success = (0,_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__.disableActivationCode)(codeId);\n                } else if (action === \"enable\") {\n                    success = (0,_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__.enableActivationCode)(codeId);\n                }\n                if (success) {\n                    res.status(200).json({\n                        success: true\n                    });\n                } else {\n                    res.status(400).json({\n                        error: \"Failed to update activation code\"\n                    });\n                }\n            } catch (error) {\n                res.status(500).json({\n                    error: \"Failed to update activation code\"\n                });\n            }\n            break;\n        case \"DELETE\":\n            // 删除激活码\n            try {\n                // 从URL查询参数获取codeId\n                const codeId = req.query?.codeId || req.query?.id;\n                if (!codeId) {\n                    return res.status(400).json({\n                        error: \"Missing codeId parameter\"\n                    });\n                }\n                const success = (0,_lib_activation_codes__WEBPACK_IMPORTED_MODULE_0__.deleteActivationCode)(codeId);\n                if (success) {\n                    res.status(200).json({\n                        success: true\n                    });\n                } else {\n                    res.status(400).json({\n                        error: \"Failed to delete activation code - code not found or already deleted\"\n                    });\n                }\n            } catch (error) {\n                res.status(500).json({\n                    error: \"Failed to delete activation code\"\n                });\n            }\n            break;\n        default:\n            res.setHeader(\"Allow\", [\n                \"GET\",\n                \"POST\",\n                \"PUT\",\n                \"DELETE\"\n            ]);\n            res.status(405).end(`Method ${method} Not Allowed`);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/activation-codes.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/admin/activation-codes.js"));
module.exports = __webpack_exports__;

})();