// pages/api/auth/google/admin-callback.js
import cookie from 'cookie'

const ADMIN_EMAIL = '<EMAIL>'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET'])
    return res.status(405).end(`Method ${req.method} Not Allowed`)
  }

  const { code, state, error } = req.query
  
  // 检查是否有错误
  if (error) {
    console.error('Google OAuth error:', error)
    return res.redirect('/admin/login?error=oauth_error')
  }

  // 验证state参数
  const cookies = cookie.parse(req.headers.cookie || '')
  if (cookies.googleOAuthState !== state) {
    console.error('Invalid state parameter')
    return res.redirect('/admin/login?error=invalid_state')
  }

  if (!code) {
    console.error('No authorization code received')
    return res.redirect('/admin/login?error=no_code')
  }

  try {
    // 交换授权码获取访问令牌
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_OAUTH_CLIENT_ID,
        client_secret: process.env.GOOGLE_OAUTH_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: process.env.GOOGLE_OAUTH_REDIRECT_URI.replace('/callback', '/admin-callback'),
      }),
    })

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text()
      console.error('Token exchange error:', errorText)
      return res.redirect('/admin/login?error=token_error')
    }

    const tokenData = await tokenResponse.json()
    const { access_token } = tokenData

    // 获取用户信息
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    })

    if (!userResponse.ok) {
      console.error('Failed to fetch user info')
      return res.redirect('/admin/login?error=user_info_error')
    }

    const userData = await userResponse.json()
    const { email, name, picture } = userData

    // 验证是否为管理员邮箱
    if (email !== ADMIN_EMAIL) {
      console.log(`Unauthorized admin login attempt: ${email}`)
      return res.redirect('/admin/login?error=unauthorized')
    }

    // 创建管理员session token
    const SESSION_SECRET = process.env.SESSION_SECRET || 'your-secret-key'
    const sessionToken = Buffer.from(`admin:${Date.now()}:${SESSION_SECRET}`).toString('base64')

    // 设置管理员session cookies
    const sessionCookies = [
      cookie.serialize('adminSession', sessionToken, {
        path: '/',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 24 * 60 * 60 // 24小时
      }),
      cookie.serialize('adminEmail', email, { 
        path: '/', 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production', 
        sameSite: 'lax',
        maxAge: 24 * 60 * 60
      }),
      cookie.serialize('adminName', name, { 
        path: '/', 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production', 
        sameSite: 'lax',
        maxAge: 24 * 60 * 60
      }),
      cookie.serialize('adminPicture', picture || '', { 
        path: '/', 
        httpOnly: true, 
        secure: process.env.NODE_ENV === 'production', 
        sameSite: 'lax',
        maxAge: 24 * 60 * 60
      }),
      // 清除OAuth状态cookie
      cookie.serialize('googleOAuthState', '', { 
        path: '/', 
        expires: new Date(0) 
      })
    ]

    res.setHeader('Set-Cookie', sessionCookies)

    // 登录成功，重定向到管理员面板
    res.redirect('/admin/dashboard')

  } catch (error) {
    console.error('Admin OAuth callback error:', error)
    res.redirect('/admin/login?error=server_error')
  }
}
