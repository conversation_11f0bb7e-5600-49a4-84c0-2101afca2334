"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/admin/dashboard";
exports.ids = ["pages/admin/dashboard"];
exports.modules = {

/***/ "./pages/admin/dashboard.js":
/*!**********************************!*\
  !*** ./pages/admin/dashboard.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nfunction AdminDashboard() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [codes, setCodes] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"codes\") // 'codes' 或 'users'\n    ;\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [newCodeForm, setNewCodeForm] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        description: \"\",\n        expiresInDays: 30,\n        maxUsageCount: 1\n    });\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 检查认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    // 加载激活码数据\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (isAuthenticated) {\n            loadActivationCodes();\n            loadUsers();\n        }\n    }, [\n        isAuthenticated\n    ]);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/auth\");\n            const result = await response.json();\n            if (result.authenticated) {\n                setIsAuthenticated(true);\n            } else {\n                router.push(\"/admin/login\");\n            }\n        } catch (error) {\n            router.push(\"/admin/login\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadActivationCodes = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\");\n            if (response.ok) {\n                const data = await response.json();\n                setCodes(data.codes);\n                setStats(data.stats);\n            }\n        } catch (error) {\n            console.error(\"Failed to load activation codes:\", error);\n        }\n    };\n    const loadUsers = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/users\");\n            if (response.ok) {\n                const data = await response.json();\n                setUsers(data.users);\n            }\n        } catch (error) {\n            console.error(\"Failed to load users:\", error);\n        }\n    };\n    const handleCreateCode = async (e)=>{\n        e.preventDefault();\n        setIsCreating(true);\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newCodeForm)\n            });\n            if (response.ok) {\n                setNewCodeForm({\n                    description: \"\",\n                    expiresInDays: 30,\n                    maxUsageCount: 1\n                });\n                setShowCreateForm(false);\n                loadActivationCodes() // 重新加载数据\n                ;\n                loadUsers() // 重新加载用户数据\n                ;\n            } else {\n                alert(\"创建激活码失败\");\n            }\n        } catch (error) {\n            alert(\"创建激活码失败\");\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    const handleToggleCode = async (codeId, action)=>{\n        try {\n            const response = await fetch(\"/api/admin/activation-codes\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    codeId,\n                    action\n                })\n            });\n            if (response.ok) {\n                loadActivationCodes() // 重新加载数据\n                ;\n                loadUsers() // 重新加载用户数据\n                ;\n            } else {\n                alert(\"操作失败\");\n            }\n        } catch (error) {\n            alert(\"操作失败\");\n        }\n    };\n    const handleDeleteCode = async (codeId)=>{\n        if (!confirm(\"确定要删除这个激活码吗？\")) {\n            return;\n        }\n        try {\n            const response = await fetch(`/api/admin/activation-codes?codeId=${encodeURIComponent(codeId)}`, {\n                method: \"DELETE\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                loadActivationCodes() // 重新加载数据\n                ;\n                loadUsers() // 重新加载用户数据\n                ;\n            } else {\n                const errorData = await response.json().catch(()=>({}));\n                alert(`删除失败: ${errorData.error || \"未知错误\"}`);\n            }\n        } catch (error) {\n            alert(\"删除失败: \" + error.message);\n        }\n    };\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/admin/auth\", {\n                method: \"DELETE\"\n            });\n            router.push(\"/admin/login\");\n        } catch (error) {\n            router.push(\"/admin/login\");\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleString(\"zh-CN\");\n    };\n    const getStatusBadge = (code)=>{\n        const usedCount = code.usedCount || 0;\n        const maxUsageCount = code.maxUsageCount || 1;\n        // 检查是否已达到使用次数上限\n        if (usedCount >= maxUsageCount) return {\n            text: \"已用完\",\n            class: \"used\"\n        };\n        if (!code.isActive) return {\n            text: \"已禁用\",\n            class: \"disabled\"\n        };\n        if (new Date() > new Date(code.expiresAt)) return {\n            text: \"已过期\",\n            class: \"expired\"\n        };\n        // 部分使用状态\n        if (usedCount > 0 && maxUsageCount > 1) return {\n            text: \"部分使用\",\n            class: \"partial\"\n        };\n        return {\n            text: \"可用\",\n            class: \"available\"\n        };\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"loading-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"spinner\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"加载中...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null // 会被重定向到登录页\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-dc7bfbe62498de9d\",\n                    children: \"激活码管理 - Great Heights School\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"main-card\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"header\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"header-content\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"header-left\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"logo-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/resources/logo.jpg\",\n                                                        alt: \"Great Heights School Logo\",\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"logo-image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"title-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                children: \"激活码管理系统\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"subtitle\",\n                                                                children: \"Great Heights School\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"header-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"header-actions\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowCreateForm(true),\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"btn btn-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M12 5v14m-7-7h14\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-dc7bfbe62498de9d\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"生成激活码\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"btn btn-secondary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"16\",\n                                                                height: \"16\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"登出\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"main-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"tabs-navigation\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"codes\"),\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + `tab-button ${activeTab === \"codes\" ? \"active\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"jsx-dc7bfbe62498de9d\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                            fill: \"currentColor\",\n                                                            className: \"jsx-dc7bfbe62498de9d\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"激活码管理\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"users\"),\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + `tab-button ${activeTab === \"users\" ? \"active\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        className: \"jsx-dc7bfbe62498de9d\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                className: \"jsx-dc7bfbe62498de9d\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"9\",\n                                                                cy: \"7\",\n                                                                r: \"4\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                className: \"jsx-dc7bfbe62498de9d\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"m22 21-3-3m0 0a2 2 0 1 0-4 0 2 2 0 0 0 4 0z\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                className: \"jsx-dc7bfbe62498de9d\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"用户列表\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    activeTab === \"codes\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stats-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\",\n                                                        children: \"数据概览\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stats-grid\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-icon total\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-dc7bfbe62498de9d\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 272,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 271,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 270,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                                children: \"总计\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 275,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-number\",\n                                                                        children: stats.total || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-desc\",\n                                                                        children: \"激活码总数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-icon available\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-dc7bfbe62498de9d\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 284,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 283,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 282,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                                children: \"可用\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-number available\",\n                                                                        children: stats.available || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-desc\",\n                                                                        children: \"可正常使用\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-icon used\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-dc7bfbe62498de9d\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 296,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                                children: \"已使用\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-number used\",\n                                                                        children: stats.used || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-desc\",\n                                                                        children: \"已被激活\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-icon expired\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-dc7bfbe62498de9d\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-6V7h2v4h4v2z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 308,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                                children: \"已过期\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-number expired\",\n                                                                        children: stats.expired || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-desc\",\n                                                                        children: \"超过有效期\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-icon multi-use\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    fill: \"none\",\n                                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                                    className: \"jsx-dc7bfbe62498de9d\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\",\n                                                                                        fill: \"currentColor\",\n                                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 23\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 319,\n                                                                                    columnNumber: 21\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 19\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                                children: \"总使用次数\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 323,\n                                                                                columnNumber: 19\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-number multi-use\",\n                                                                        children: stats.totalUsages || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 17\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"stat-desc\",\n                                                                        children: \"累计激活次数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 17\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 13\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                onClick: ()=>setShowCreateForm(false),\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"modal-overlay\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: (e)=>e.stopPropagation(),\n                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"modal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                            children: \"生成新激活码\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                            onSubmit: handleCreateCode,\n                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"form-group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: \"描述\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: newCodeForm.description,\n                                                                            onChange: (e)=>setNewCodeForm({\n                                                                                    ...newCodeForm,\n                                                                                    description: e.target.value\n                                                                                }),\n                                                                            placeholder: \"激活码用途描述（可选）\",\n                                                                            className: \"jsx-dc7bfbe62498de9d\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 336,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"form-group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: \"有效期（天）\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: newCodeForm.expiresInDays,\n                                                                            onChange: (e)=>setNewCodeForm({\n                                                                                    ...newCodeForm,\n                                                                                    expiresInDays: parseInt(e.target.value)\n                                                                                }),\n                                                                            min: \"1\",\n                                                                            max: \"365\",\n                                                                            required: true,\n                                                                            className: \"jsx-dc7bfbe62498de9d\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"form-group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: \"最大使用次数\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: newCodeForm.maxUsageCount,\n                                                                            onChange: (e)=>setNewCodeForm({\n                                                                                    ...newCodeForm,\n                                                                                    maxUsageCount: parseInt(e.target.value)\n                                                                                }),\n                                                                            min: \"1\",\n                                                                            max: \"1000\",\n                                                                            required: true,\n                                                                            className: \"jsx-dc7bfbe62498de9d\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"form-help\",\n                                                                            children: \"设置激活码可以被使用的最大次数（1表示一次性使用）\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 17\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"form-actions\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowCreateForm(false),\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"btn btn-secondary\",\n                                                                            children: \"取消\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"submit\",\n                                                                            disabled: isCreating,\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"btn btn-primary\",\n                                                                            children: isCreating ? \"生成中...\" : \"生成\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 13\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 332,\n                                                columnNumber: 11\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"codes-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"section-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                children: \"激活码列表\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 15\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"section-actions\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"search-box\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            width: \"16\",\n                                                                            height: \"16\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            fill: \"none\",\n                                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"m21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"2\",\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                className: \"jsx-dc7bfbe62498de9d\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 21\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 19\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"搜索激活码...\",\n                                                                            className: \"jsx-dc7bfbe62498de9d\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 19\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 17\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 15\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 13\n                                                    }, this),\n                                                    codes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"empty-state\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"empty-icon\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    width: \"48\",\n                                                                    height: \"48\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    fill: \"none\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"jsx-dc7bfbe62498de9d\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M9 11H7v8h2v-8zm4-4h-2v12h2V7zm4-2h-2v14h2V5z\",\n                                                                        fill: \"currentColor\",\n                                                                        className: \"jsx-dc7bfbe62498de9d\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                children: \"暂无激活码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\",\n                                                                children: '点击\"生成激活码\"按钮创建第一个激活码'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 15\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"codes-table\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"table-header\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-code\",\n                                                                        children: \"激活码\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-status\",\n                                                                        children: \"状态\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-usage\",\n                                                                        children: \"使用次数\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-created\",\n                                                                        children: \"创建时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-expires\",\n                                                                        children: \"过期时间\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-user\",\n                                                                        children: \"最后使用者\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-actions\",\n                                                                        children: \"操作\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            codes.map((code)=>{\n                                                                const status = getStatusBadge(code);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"table-row\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"code-cell\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"code-text\",\n                                                                                    children: code.code\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 427,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                code.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"code-desc\",\n                                                                                    children: code.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 426,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + `status-badge ${status.class}`,\n                                                                                children: status.text\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 432,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"usage-info\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"usage-count\",\n                                                                                    children: [\n                                                                                        code.usedCount || 0,\n                                                                                        \" / \",\n                                                                                        code.maxUsageCount || 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 438,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                (code.maxUsageCount || 1) > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"usage-type\",\n                                                                                    children: \"多次使用\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 442,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: formatDate(code.createdAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: formatDate(code.expiresAt)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                                            children: code.usedBy || \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"actions\",\n                                                                            children: [\n                                                                                (code.usedCount || 0) < (code.maxUsageCount || 1) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleToggleCode(code.id, code.isActive ? \"disable\" : \"enable\"),\n                                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + `btn btn-sm ${code.isActive ? \"btn-warning\" : \"btn-success\"}`,\n                                                                                    children: code.isActive ? \"禁用\" : \"启用\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>handleDeleteCode(code.id),\n                                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"btn btn-sm btn-danger\",\n                                                                                    children: \"删除\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, code.id, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 19\n                                                                }, this);\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 389,\n                                                columnNumber: 11\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    activeTab === \"users\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"users-section\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"section-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\",\n                                                        children: \"用户列表\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"section-info\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"info-text\",\n                                                            children: [\n                                                                \"共 \",\n                                                                users.length,\n                                                                \" 个用户\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"empty-state\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"empty-icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"48\",\n                                                            height: \"48\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"jsx-dc7bfbe62498de9d\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-dc7bfbe62498de9d\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"9\",\n                                                                    cy: \"7\",\n                                                                    r: \"4\",\n                                                                    stroke: \"currentColor\",\n                                                                    strokeWidth: \"2\",\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    className: \"jsx-dc7bfbe62498de9d\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\",\n                                                        children: \"暂无用户\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\",\n                                                        children: \"还没有用户使用激活码注册\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"users-table\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"table-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-email\",\n                                                                children: \"用户邮箱\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-code\",\n                                                                children: \"使用的激活码\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-description\",\n                                                                children: \"激活码描述\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-usage-info\",\n                                                                children: \"使用情况\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"col-used-time\",\n                                                                children: \"使用时间\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    users.map((user, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"table-row\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"email-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"email-text\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"code-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"code-text\",\n                                                                        children: user.activationCode\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"description-cell\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"description-text\",\n                                                                        children: user.codeDescription\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"usage-cell\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"usage-count\",\n                                                                            children: [\n                                                                                user.currentUsedCount,\n                                                                                \" / \",\n                                                                                user.maxUsageCount\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 515,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        user.maxUsageCount > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"usage-type\",\n                                                                            children: \"多次使用\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                            lineNumber: 519,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"time-cell\",\n                                                                    children: formatDate(user.usedAt)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, `${user.email}-${user.activationCode}-${index}`, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"footer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-dc7bfbe62498de9d\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"powered-by\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"14\",\n                                        height: \"14\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"currentColor\",\n                                        className: \"jsx-dc7bfbe62498de9d\" + \" \" + \"icon\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\",\n                                            className: \"jsx-dc7bfbe62498de9d\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 538,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Powered by\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://www.ghs.red/\",\n                                        target: \"_blank\",\n                                        rel: \"noopener\",\n                                        className: \"jsx-dc7bfbe62498de9d\",\n                                        children: \"Garbage Human Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                        lineNumber: 535,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\admin\\\\dashboard.js\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"dc7bfbe62498de9d\",\n                children: '.loading-container.jsx-dc7bfbe62498de9d{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.spinner.jsx-dc7bfbe62498de9d{width:32px;height:32px;border:3px solid#e1e5e9;border-top:3px solid#111827;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-animation:spin.8s linear infinite;-moz-animation:spin.8s linear infinite;-o-animation:spin.8s linear infinite;animation:spin.8s linear infinite;margin-bottom:16px}@-webkit-keyframes spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin{0%{-moz-transform:rotate(0deg);transform:rotate(0deg)}100%{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin{0%{-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.container.jsx-dc7bfbe62498de9d{min-height:100vh;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;background:#fafafa;padding:20px;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.main-card.jsx-dc7bfbe62498de9d{background:#fff;max-width:1200px;width:100%;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);overflow:hidden}.header.jsx-dc7bfbe62498de9d{background:#fff;border-bottom:1px solid#e1e5e9;padding:20px 32px}.header-content.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-left.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-right.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.logo-section.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:16px}.logo-image.jsx-dc7bfbe62498de9d{width:32px;height:32px;-o-object-fit:contain;object-fit:contain;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.title-section.jsx-dc7bfbe62498de9d h1.jsx-dc7bfbe62498de9d{color:#111827;margin:0;font-size:24px;font-weight:600;letter-spacing:-.02em;line-height:1.2}.subtitle.jsx-dc7bfbe62498de9d{color:#6b7280;margin:2px 0 0 0;font-size:15px;font-weight:400;line-height:1.2}.header-actions.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.main-content.jsx-dc7bfbe62498de9d{padding:32px}.tabs-navigation.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;border-bottom:2px solid#e1e5e9;margin-bottom:32px;gap:4px}.tab-button.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:8px;padding:12px 20px;border:none;background:transparent;color:#6b7280;font-size:14px;font-weight:500;cursor:pointer;-webkit-border-radius:8px 8px 0 0;-moz-border-radius:8px 8px 0 0;border-radius:8px 8px 0 0;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease;position:relative}.tab-button.jsx-dc7bfbe62498de9d:hover{background:#f3f4f6;color:#374151}.tab-button.active.jsx-dc7bfbe62498de9d{background:#fff;color:#111827;border-bottom:2px solid#2563eb;margin-bottom:-2px}.tab-button.jsx-dc7bfbe62498de9d svg.jsx-dc7bfbe62498de9d{width:16px;height:16px}.stats-section.jsx-dc7bfbe62498de9d{margin-bottom:48px}.stats-section.jsx-dc7bfbe62498de9d h2.jsx-dc7bfbe62498de9d{color:#111827;font-size:20px;font-weight:600;margin-bottom:24px;letter-spacing:-.02em}.stats-grid.jsx-dc7bfbe62498de9d{display:grid;grid-template-columns:repeat(auto-fit,minmax(240px,1fr));gap:20px}.stat-card.jsx-dc7bfbe62498de9d{background:#fff;padding:24px;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1);-webkit-transition:all.2s ease-out;-moz-transition:all.2s ease-out;-o-transition:all.2s ease-out;transition:all.2s ease-out}.stat-card.jsx-dc7bfbe62498de9d:hover{border-color:#d1d5db;-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.08);-moz-box-shadow:0 4px 12px rgba(0,0,0,.08);box-shadow:0 4px 12px rgba(0,0,0,.08)}.stat-header.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px;margin-bottom:16px}.stat-icon.jsx-dc7bfbe62498de9d{width:40px;height:40px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.stat-icon.total.jsx-dc7bfbe62498de9d{background:#f3f4f6;color:#374151}.stat-icon.available.jsx-dc7bfbe62498de9d{background:#ecfdf5;color:#16a34a}.stat-icon.used.jsx-dc7bfbe62498de9d{background:#eff6ff;color:#2563eb}.stat-icon.expired.jsx-dc7bfbe62498de9d{background:#fef2f2;color:#dc2626}.stat-icon.multi-use.jsx-dc7bfbe62498de9d{background:#fef3c7;color:#d97706}.stat-card.jsx-dc7bfbe62498de9d h3.jsx-dc7bfbe62498de9d{margin:0;color:#374151;font-size:14px;font-weight:500}.stat-number.jsx-dc7bfbe62498de9d{font-size:28px;font-weight:700;color:#111827;margin-bottom:4px}.stat-desc.jsx-dc7bfbe62498de9d{color:#6b7280;font-size:13px;margin:0}.stat-number.available.jsx-dc7bfbe62498de9d{color:#16a34a}.stat-number.used.jsx-dc7bfbe62498de9d{color:#2563eb}.stat-number.expired.jsx-dc7bfbe62498de9d{color:#dc2626}.codes-section.jsx-dc7bfbe62498de9d{background:#fff;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;border:1px solid#e1e5e9;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.1);-moz-box-shadow:0 1px 3px rgba(0,0,0,.1);box-shadow:0 1px 3px rgba(0,0,0,.1)}.section-header.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:24px 24px 0 24px;margin-bottom:24px}.section-header.jsx-dc7bfbe62498de9d h2.jsx-dc7bfbe62498de9d{margin:0;color:#111827;font-size:18px;font-weight:600;letter-spacing:-.02em}.section-actions.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px}.search-box.jsx-dc7bfbe62498de9d{position:relative;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.search-box.jsx-dc7bfbe62498de9d svg.jsx-dc7bfbe62498de9d{position:absolute;left:12px;color:#9ca3af;z-index:1}.search-box.jsx-dc7bfbe62498de9d input.jsx-dc7bfbe62498de9d{padding:8px 12px 8px 36px;border:1px solid#d1d5db;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;font-size:14px;width:200px;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out}.search-box.jsx-dc7bfbe62498de9d input.jsx-dc7bfbe62498de9d:focus{outline:none;border-color:#111827;-webkit-box-shadow:0 0 0 3px rgba(17,24,39,.1);-moz-box-shadow:0 0 0 3px rgba(17,24,39,.1);box-shadow:0 0 0 3px rgba(17,24,39,.1)}.empty-state.jsx-dc7bfbe62498de9d{text-align:center;padding:64px 24px}.empty-icon.jsx-dc7bfbe62498de9d{margin:0 auto 16px;width:48px;height:48px;color:#d1d5db}.empty-state.jsx-dc7bfbe62498de9d h3.jsx-dc7bfbe62498de9d{color:#111827;font-size:16px;font-weight:600;margin:0 0 8px 0}.empty-state.jsx-dc7bfbe62498de9d p.jsx-dc7bfbe62498de9d{color:#6b7280;font-size:14px;margin:0}.codes-table.jsx-dc7bfbe62498de9d{overflow-x:auto}.table-header.jsx-dc7bfbe62498de9d,.table-row.jsx-dc7bfbe62498de9d{display:grid;grid-template-columns:2fr 1fr 1.5fr 1.5fr 1.5fr 1fr;gap:16px;padding:16px 24px;border-bottom:1px solid#f3f4f6;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-dc7bfbe62498de9d{font-weight:600;color:#374151;background:#f9fafb;font-size:13px;text-transform:uppercase;letter-spacing:.05em;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.code-cell.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:4px}.code-text.jsx-dc7bfbe62498de9d{font-family:monospace;font-weight:600;color:#333}.code-desc.jsx-dc7bfbe62498de9d{font-size:12px;color:#666}.status-badge.jsx-dc7bfbe62498de9d{padding:4px 8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:12px;font-weight:500}.status-badge.available.jsx-dc7bfbe62498de9d{background:#d4edda;color:#155724}.status-badge.used.jsx-dc7bfbe62498de9d{background:#e2e3e5;color:#383d41}.status-badge.disabled.jsx-dc7bfbe62498de9d{background:#f8d7da;color:#721c24}.status-badge.expired.jsx-dc7bfbe62498de9d{background:#fff3cd;color:#856404}.status-badge.partial.jsx-dc7bfbe62498de9d{background:#e0f2fe;color:#0277bd}.usage-info.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:4px}.usage-count.jsx-dc7bfbe62498de9d{font-weight:600;color:#374151}.usage-type.jsx-dc7bfbe62498de9d{font-size:11px;color:#6b7280;background:#f3f4f6;padding:2px 6px;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;-webkit-align-self:flex-start;-ms-flex-item-align:start;align-self:flex-start}.form-help.jsx-dc7bfbe62498de9d{font-size:12px;color:#6b7280;margin-top:4px;display:block}.users-section.jsx-dc7bfbe62498de9d{margin-bottom:48px}.users-table.jsx-dc7bfbe62498de9d{background:#fff;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;border:1px solid#e1e5e9;overflow:hidden}.users-table.jsx-dc7bfbe62498de9d .table-header.jsx-dc7bfbe62498de9d{background:#f8fafc;padding:16px;display:grid;grid-template-columns:2fr 1.5fr 1.5fr 1fr 1.5fr;gap:16px;font-weight:600;font-size:13px;color:#374151;text-transform:uppercase;letter-spacing:.5px}.users-table.jsx-dc7bfbe62498de9d .table-row.jsx-dc7bfbe62498de9d{padding:16px;display:grid;grid-template-columns:2fr 1.5fr 1.5fr 1fr 1.5fr;gap:16px;border-top:1px solid#e1e5e9;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.users-table.jsx-dc7bfbe62498de9d .table-row.jsx-dc7bfbe62498de9d:hover{background:#f8fafc}.email-cell.jsx-dc7bfbe62498de9d .email-text.jsx-dc7bfbe62498de9d{font-weight:500;color:#111827}.code-cell.jsx-dc7bfbe62498de9d .code-text.jsx-dc7bfbe62498de9d{font-family:\"Monaco\",\"Menlo\",\"Ubuntu Mono\",monospace;background:#f3f4f6;padding:4px 8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:12px;color:#374151}.description-cell.jsx-dc7bfbe62498de9d .description-text.jsx-dc7bfbe62498de9d{color:#6b7280;font-size:13px}.usage-cell.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:4px}.usage-cell.jsx-dc7bfbe62498de9d .usage-count.jsx-dc7bfbe62498de9d{font-weight:600;color:#374151;font-size:13px}.usage-cell.jsx-dc7bfbe62498de9d .usage-type.jsx-dc7bfbe62498de9d{font-size:11px;color:#6b7280;background:#f3f4f6;padding:2px 6px;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;-webkit-align-self:flex-start;-ms-flex-item-align:start;align-self:flex-start}.time-cell.jsx-dc7bfbe62498de9d{color:#6b7280;font-size:13px}.section-info.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px}.info-text.jsx-dc7bfbe62498de9d{color:#6b7280;font-size:14px}.actions.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:8px}.btn.jsx-dc7bfbe62498de9d{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:6px;padding:8px 16px;border:1px solid transparent;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;font-size:14px;font-weight:500;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out;text-decoration:none}.btn-primary.jsx-dc7bfbe62498de9d{background:#111827;color:#fff;border-color:#111827}.btn-primary.jsx-dc7bfbe62498de9d:hover{background:#374151;border-color:#374151;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.1);-moz-box-shadow:0 2px 4px rgba(0,0,0,.1);box-shadow:0 2px 4px rgba(0,0,0,.1)}.btn-secondary.jsx-dc7bfbe62498de9d{background:#fff;color:#374151;border-color:#d1d5db}.btn-secondary.jsx-dc7bfbe62498de9d:hover{background:#f9fafb;border-color:#9ca3af;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px);-webkit-box-shadow:0 2px 4px rgba(0,0,0,.05);-moz-box-shadow:0 2px 4px rgba(0,0,0,.05);box-shadow:0 2px 4px rgba(0,0,0,.05)}.btn-success.jsx-dc7bfbe62498de9d{background:#16a34a;color:#fff;border-color:#16a34a}.btn-success.jsx-dc7bfbe62498de9d:hover{background:#15803d;border-color:#15803d}.btn-warning.jsx-dc7bfbe62498de9d{background:#f59e0b;color:#fff;border-color:#f59e0b}.btn-warning.jsx-dc7bfbe62498de9d:hover{background:#d97706;border-color:#d97706}.btn-danger.jsx-dc7bfbe62498de9d{background:#dc2626;color:#fff;border-color:#dc2626}.btn-danger.jsx-dc7bfbe62498de9d:hover{background:#b91c1c;border-color:#b91c1c}.btn-sm.jsx-dc7bfbe62498de9d{padding:4px 8px;font-size:12px}.modal-overlay.jsx-dc7bfbe62498de9d{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.5);display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;z-index:1000}.modal.jsx-dc7bfbe62498de9d{background:#fff;padding:24px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;width:90%;max-width:400px}.modal.jsx-dc7bfbe62498de9d h2.jsx-dc7bfbe62498de9d{margin:0 0 20px 0;color:#333}.form-group.jsx-dc7bfbe62498de9d{margin-bottom:16px}.form-group.jsx-dc7bfbe62498de9d label.jsx-dc7bfbe62498de9d{display:block;margin-bottom:4px;color:#333;font-weight:500}.form-group.jsx-dc7bfbe62498de9d input.jsx-dc7bfbe62498de9d{width:100%;padding:8px 12px;border:1px solid#ddd;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-size:14px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.form-group.jsx-dc7bfbe62498de9d input.jsx-dc7bfbe62498de9d:focus{outline:none;border-color:#4285f4}.form-actions.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:12px;-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}@media(max-width:768px){.header-content.jsx-dc7bfbe62498de9d{padding:12px 16px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch}.logo.jsx-dc7bfbe62498de9d{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.header-actions.jsx-dc7bfbe62498de9d{-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.main-content.jsx-dc7bfbe62498de9d{padding:16px}.stats-grid.jsx-dc7bfbe62498de9d{grid-template-columns:1fr;gap:16px}.section-header.jsx-dc7bfbe62498de9d{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:stretch;-webkit-align-items:stretch;-moz-box-align:stretch;-ms-flex-align:stretch;align-items:stretch;padding:16px 16px 0 16px}.search-box.jsx-dc7bfbe62498de9d input.jsx-dc7bfbe62498de9d{width:100%}.table-header.jsx-dc7bfbe62498de9d,.table-row.jsx-dc7bfbe62498de9d{grid-template-columns:1fr;gap:8px;padding:12px 16px}.table-header.jsx-dc7bfbe62498de9d>div.jsx-dc7bfbe62498de9d,.table-row.jsx-dc7bfbe62498de9d>div.jsx-dc7bfbe62498de9d{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.table-header.jsx-dc7bfbe62498de9d>div.jsx-dc7bfbe62498de9d::before,.table-row.jsx-dc7bfbe62498de9d>div.jsx-dc7bfbe62498de9d::before{content:attr(data-label);font-weight:600;color:#6b7280;font-size:12px}.col-code.jsx-dc7bfbe62498de9d::before{content:\"激活码\"}.col-status.jsx-dc7bfbe62498de9d::before{content:\"状态\"}.col-created.jsx-dc7bfbe62498de9d::before{content:\"创建时间\"}.col-expires.jsx-dc7bfbe62498de9d::before{content:\"过期时间\"}.col-user.jsx-dc7bfbe62498de9d::before{content:\"使用者\"}.col-actions.jsx-dc7bfbe62498de9d::before{content:\"操作\"}}.footer.jsx-dc7bfbe62498de9d{margin-top:32px;color:#9ca3af;font-size:13px;text-align:center}.powered-by.jsx-dc7bfbe62498de9d{display:-webkit-inline-box;display:-webkit-inline-flex;display:-moz-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:4px}.icon.jsx-dc7bfbe62498de9d{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.footer.jsx-dc7bfbe62498de9d a.jsx-dc7bfbe62498de9d{color:#6b7280;text-decoration:none;font-weight:500;margin-left:4px}.footer.jsx-dc7bfbe62498de9d a.jsx-dc7bfbe62498de9d:hover{color:#374151;text-decoration:underline}@media(max-width:1023px){.main-card.jsx-dc7bfbe62498de9d{max-width:100%;margin:0;-webkit-border-radius:0;-moz-border-radius:0;border-radius:0;border-left:none;border-right:none}.container.jsx-dc7bfbe62498de9d{padding:0;-webkit-box-pack:start;-webkit-justify-content:flex-start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:flex-start}}@media(max-width:768px){.header.jsx-dc7bfbe62498de9d{padding:16px 20px}.header-content.jsx-dc7bfbe62498de9d{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:16px;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start}.header-actions.jsx-dc7bfbe62498de9d{width:100%;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}.main-content.jsx-dc7bfbe62498de9d{padding:20px}.logo-image.jsx-dc7bfbe62498de9d{width:24px;height:24px}.title-section.jsx-dc7bfbe62498de9d h1.jsx-dc7bfbe62498de9d{font-size:20px}.subtitle.jsx-dc7bfbe62498de9d{font-size:13px}}@media(max-width:480px){.header.jsx-dc7bfbe62498de9d{padding:12px 16px}.main-content.jsx-dc7bfbe62498de9d{padding:16px}.stats-grid.jsx-dc7bfbe62498de9d{grid-template-columns:1fr;gap:16px}.stat-card.jsx-dc7bfbe62498de9d{padding:20px}.btn.jsx-dc7bfbe62498de9d{padding:8px 12px;font-size:13px}.header-actions.jsx-dc7bfbe62498de9d{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;gap:8px}}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/admin/dashboard.js\n");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "next/router":
/*!******************************!*\
  !*** external "next/router" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("next/router");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/admin/dashboard.js"));
module.exports = __webpack_exports__;

})();