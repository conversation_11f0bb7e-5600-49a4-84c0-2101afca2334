// pages/api/admin/auth.js
import cookie from 'cookie'

const SESSION_SECRET = process.env.SESSION_SECRET || 'your-secret-key'
const ADMIN_EMAIL = '<EMAIL>'

export default function handler(req, res) {
  if (req.method === 'GET') {
    // 验证管理员session
    const cookies = cookie.parse(req.headers.cookie || '')
    const sessionToken = cookies.adminSession
    const adminEmail = cookies.adminEmail

    if (!sessionToken || !adminEmail) {
      return res.status(401).json({ authenticated: false })
    }

    // 验证邮箱是否为管理员邮箱
    if (adminEmail !== ADMIN_EMAIL) {
      return res.status(401).json({ authenticated: false })
    }

    try {
      const decoded = Buffer.from(sessionToken, 'base64').toString()
      const [user, timestamp, secret] = decoded.split(':')

      if (user !== 'admin' || secret !== SESSION_SECRET) {
        return res.status(401).json({ authenticated: false })
      }

      // 检查session是否过期（24小时）
      const sessionTime = parseInt(timestamp)
      const now = Date.now()
      const maxAge = 24 * 60 * 60 * 1000 // 24小时

      if (now - sessionTime > maxAge) {
        return res.status(401).json({ authenticated: false, message: 'Session过期' })
      }

      res.status(200).json({ authenticated: true, email: adminEmail })
    } catch (error) {
      res.status(401).json({ authenticated: false })
    }
  } else if (req.method === 'DELETE') {
    // 登出 - 清除所有管理员相关的cookies
    const clearCookies = [
      cookie.serialize('adminSession', '', {
        path: '/',
        expires: new Date(0)
      }),
      cookie.serialize('adminEmail', '', {
        path: '/',
        expires: new Date(0)
      }),
      cookie.serialize('adminName', '', {
        path: '/',
        expires: new Date(0)
      }),
      cookie.serialize('adminPicture', '', {
        path: '/',
        expires: new Date(0)
      })
    ]

    res.setHeader('Set-Cookie', clearCookies)
    res.status(200).json({ success: true, message: '已登出' })
  } else {
    res.setHeader('Allow', ['GET', 'DELETE'])
    res.status(405).end(`Method ${req.method} Not Allowed`)
  }
}
