// pages/student-card.js
import Head from 'next/head'
import <PERSON>rip<PERSON> from 'next/script'
import { parse } from 'cookie'

// Helper to fetch a Google user from Directory
async function fetchGoogleUser(email) {
  const tokenRes = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'Content-Type':'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      client_id:     process.env.GOOGLE_CLIENT_ID,
      client_secret: process.env.GOOGLE_CLIENT_SECRET,
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
      grant_type:    'refresh_token'
    })
  })
  if (!tokenRes.ok) return null
  const { access_token } = await tokenRes.json()
  const userRes = await fetch(
    `https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(email)}`,
    { headers:{ Authorization:`Bearer ${access_token}` } }
  )
  if (!userRes.ok) return null
  return await userRes.json()
}

export async function getServerSideProps({ req }) {
  const cookies       = parse(req.headers.cookie||'')
  const userEmail = cookies.userEmail
  const userName = cookies.userName

  // 检查是否已登录（通过Google OAuth）
  if (!userEmail || !userName) {
    return { redirect: { destination: '/login', permanent: false } }
  }

  // 验证邮箱域名
  const allowedDomain = process.env.EMAIL_DOMAIN || 'ghs.edu.kg'
  const domain = allowedDomain.startsWith('@') ? allowedDomain.slice(1) : allowedDomain

  if (!userEmail.endsWith(`@${domain}`)) {
    return { redirect: { destination: '/login?error=invalid_domain', permanent: false } }
  }

  // 获取Google Directory中的用户信息
  const googleUser = await fetchGoogleUser(userEmail)
  if (!googleUser) {
    return { redirect: { destination: '/login?error=user_not_found', permanent: false } }
  }

  const fullName = `${googleUser.name.givenName} ${googleUser.name.familyName}`
  const personalEmail = googleUser.recoveryEmail || ''
  const studentId = userEmail.split('@')[0]

  return {
    props: { fullName, personalEmail, studentEmail: userEmail, studentId }
  }
}

export default function StudentCard({
  fullName,
  personalEmail,
  studentEmail,
  studentId
}) {
  const sid = String(studentId).padStart(6,'0')
  const avatarUrl = `https://i.pravatar.cc/150?u=${encodeURIComponent(studentEmail)}`

  return (
    <>
      <Head><title>Student ID Card - ChatGPT University</title></Head>
      <Script
        src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"
        strategy="afterInteractive"
        onLoad={() => {
          if (window.JsBarcode) {
            window.JsBarcode('#barcode', sid, {
              format: 'CODE128',
              lineColor: '#000',
              width: 2,
              height: 50,
              displayValue: true
            })
          }
        }}
      />

      <div className="wrapper">
        <div className="card">
          {/* School header with logo and name */}
          <div className="school-header">
            <img src="/resources/logo.jpg" alt="Great Heights School Logo" className="school-logo" />
            <h1>Great Heights School</h1>
          </div>

          <div className="card-body">
            <img src={avatarUrl} alt="Photo" className="student-photo" />
            <h3>{fullName}</h3>
            <p>Fall 2025</p>
            <p>Master of Computer Science</p>
            <p>{studentEmail}</p>
            <p><strong>Student ID:</strong> {sid}</p>
            <p className="valid-through">Valid Through: September 2028</p>
            <div className="barcode">
              <svg id="barcode" width="200" height="60"></svg>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .wrapper {
          min-height:100vh;
          display:flex;justify-content:center;align-items:center;
          background:url('https://png.pngtree.com/thumb_back/fw800/background/20231028/pngtree-stunning-isolated-wood-table-top-texture-with-exquisite-texture-image_13705698.png')
            center/cover no-repeat;
          padding:20px;
        }
        .card {
          width:400px;
          background:linear-gradient(145deg,#e6e6e6,#ffffff);
          border:1px solid #ccc;border-radius:10px;
          box-shadow:0 12px 32px rgba(0,0,0,0.2);overflow:hidden;
        }
        .school-header {
          display:flex;align-items:center;gap:8px;
          background:linear-gradient(to right,#0062cc,#0096ff);
          padding:12px 16px;
        }
        .school-logo {
          width: 36px;
          height: 36px;
          object-fit: contain;
          border-radius: 4px;
        }
        .school-header h1 {
          margin:0;
          font-size:20px;
          color:#fff;
        }
        .card-body {
          background:#fff;padding:20px;text-align:center;
        }
        .student-photo {
          width:100px;height:100px;object-fit:cover;
          border:3px solid #007bff;border-radius:50%;
          box-shadow:0 4px 12px rgba(0,0,0,0.3);margin-bottom:12px;
        }
        h3 {
          margin:8px 0;font-size:20px;color:#333;
        }
        p {
          margin:6px 0;font-size:16px;color:#555;
        }
        .valid-through {
          margin-top:12px;font-weight:bold;color:#444;
        }
        .barcode {
          margin-top:20px;
        }
      `}</style>
    </>
  )
}
