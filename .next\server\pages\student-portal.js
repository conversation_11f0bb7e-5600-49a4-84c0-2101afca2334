"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/student-portal";
exports.ids = ["pages/student-portal"];
exports.modules = {

/***/ "./data/student-benefits.js":
/*!**********************************!*\
  !*** ./data/student-benefits.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"benefitCategories\": () => (/* binding */ benefitCategories),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   \"getAllCategories\": () => (/* binding */ getAllCategories),\n/* harmony export */   \"getBenefitById\": () => (/* binding */ getBenefitById),\n/* harmony export */   \"getBenefitsByCategory\": () => (/* binding */ getBenefitsByCategory),\n/* harmony export */   \"getBenefitsCount\": () => (/* binding */ getBenefitsCount),\n/* harmony export */   \"studentBenefits\": () => (/* binding */ studentBenefits)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * 学生权益配置文件\n *\n * 此文件包含所有学生可享受的权益和服务信息\n *\n * 数据结构说明：\n * - id: 唯一标识符（数字）\n * - name: 权益服务名称\n * - category: 分类标签\n * - description: 详细描述\n * - icon: 图标配置对象\n *   - type: 图标类型 ('svg' | 'image')\n *   - content: SVG JSX元素 或 图片URL字符串\n *   - alt: 图片的替代文本（仅当type为'image'时需要）\n * - link: 服务链接URL\n *\n * 图标类型说明：\n * 1. SVG图标：{ type: 'svg', content: <svg>...</svg> }\n * 2. 图片图标：{ type: 'image', content: '/path/to/image.png', alt: '图标描述' }\n *\n * 添加新权益的步骤：\n * 1. 在 studentBenefits 数组中添加新对象\n * 2. 确保 id 是唯一的数字\n * 3. 根据需要选择 SVG 或图片图标\n * 4. 测试链接的有效性\n */ // 学生权益数据配置\n\nconst studentBenefits = [\n    {\n        id: 1,\n        name: \"Notion 教育 Plus 版\",\n        category: \"学习工具\",\n        description: \"强大的笔记软件，免费使用Plus版本，无限制区块数量。直接使用@ghs.edu.kg邮箱登入即可。\",\n        icon: {\n            type: \"svg\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                t: \"1755830388838\",\n                class: \"icon\",\n                viewBox: \"0 0 1024 1024\",\n                version: \"1.1\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                \"p-id\": \"5206\",\n                width: \"200\",\n                height: \"200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M647.165837 2.332635L78.85493 44.179432C33.04873 48.138835 17.074585 78.005024 17.074585 113.810318v621.148461c0 27.886487 9.966774 51.745305 33.893858 83.625329l133.595729 173.189762c21.947382 27.886487 41.88093 33.859725 83.795993 31.880024l659.957441-39.832963c55.807107-3.959403 71.781251-29.866189 71.781251-73.658555V211.361824c0-22.630038-8.976923-29.1494-35.3957-48.468558a1594.683818 1594.683818 0 0 1-4.505528-3.276748L778.781865 32.198823C734.8871 0.387066 716.967387-3.640603 647.165837 2.332635zM283.310326 199.92734c-53.895671 3.618075-66.115209 4.437262-96.732319-20.377274l-77.822755-61.712079c-7.918807-7.987072-3.925271-17.953846 15.974144-19.933548l546.363525-39.79883c45.840333-3.993536 69.767417 11.946476 87.721263 25.872653l93.728634 67.71945c3.993536 1.979702 13.926177 13.892044 1.979702 13.892044l-564.249106 33.859725-6.963088 0.477859zM220.471864 904.189138V310.961297c0-25.906785 7.952939-37.853261 31.880024-39.867096l648.010965-37.819128c21.981515-1.979702 31.948289 11.946476 31.948289 37.819128v589.268439c0 25.906785-3.993536 47.820035-39.935361 49.799736l-620.090346 35.839427c-35.873559 1.979702-51.813571-9.932641-51.813571-41.812665z m612.171539-561.416084c3.959403 17.919713 0 35.839427-17.987979 37.887394l-29.900321 5.904972v437.991926c-25.940918 13.926177-49.833869 21.879117-69.767417 21.879116-31.914156 0-39.935361-9.966774-63.828313-39.79883l-195.444339-306.580694v296.613921l61.84861 13.96031s0 35.839427-49.902135 35.839426l-137.555132 7.95294c-3.959403-7.987072 0-27.886487 13.994443-31.845891l35.839426-9.932641v-392.185725l-49.833869-4.027669c-3.959403-17.919713 5.973238-43.792366 33.92799-45.772068l147.55604-9.966773 203.397279 310.608363v-274.768937l-51.881837-5.939105c-3.959403-21.947382 11.946476-37.853261 31.914156-39.832962l137.623398-7.987073z\",\n                    fill: \"#000000\",\n                    \"p-id\": \"5207\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                    lineNumber: 40,\n                    columnNumber: 155\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, undefined)\n        },\n        link: \"https://www.notion.so/product/notion-for-education\"\n    },\n    {\n        id: 2,\n        name: \"Figma Education\",\n        category: \"设计工具\",\n        description: \"免费获得Figma Professional版本的所有权益。使用@ghs.edu.kg邮箱登录验证权益即可。\",\n        icon: {\n            type: \"svg\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                t: \"1755831085205\",\n                class: \"icon\",\n                viewBox: \"0 0 1024 1024\",\n                version: \"1.1\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                \"p-id\": \"6228\",\n                width: \"200\",\n                height: \"200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M208.979592 1024h606.040816c115.461224 0 208.979592-93.518367 208.979592-208.979592V208.979592C1024 93.518367 930.481633 0 815.020408 0H208.979592C93.518367 0 0 93.518367 0 208.979592v606.040816c0 115.461224 93.518367 208.979592 208.979592 208.979592z\",\n                        fill: \"#231C23\",\n                        \"p-id\": \"6229\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 53,\n                        columnNumber: 155\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M391.836735 391.836735H512v-240.326531H391.836735C325.485714 151.510204 271.673469 205.322449 271.673469 271.673469s53.812245 120.163265 120.163266 120.163266z\",\n                        fill: \"#FF683C\",\n                        \"p-id\": \"6230\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 53,\n                        columnNumber: 451\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M391.836735 632.163265H512v-240.32653H391.836735C325.485714 391.836735 271.673469 445.64898 271.673469 512s53.812245 120.163265 120.163266 120.163265z\",\n                        fill: \"#AB6AFF\",\n                        \"p-id\": \"6231\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 53,\n                        columnNumber: 655\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M391.836735 872.489796C458.187755 872.489796 512 818.677551 512 752.326531V632.163265H391.836735C325.485714 632.163265 271.673469 685.97551 271.673469 752.326531s53.812245 120.163265 120.163266 120.163265z\",\n                        fill: \"#17DC90\",\n                        \"p-id\": \"6232\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 53,\n                        columnNumber: 850\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M512 391.836735h120.163265C698.514286 391.836735 752.326531 338.02449 752.326531 271.673469s-53.812245-120.163265-120.163266-120.163265H512v240.326531z\",\n                        fill: \"#FF8678\",\n                        \"p-id\": \"6233\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 53,\n                        columnNumber: 1100\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M632.163265 632.163265C698.514286 632.163265 752.326531 578.35102 752.326531 512s-53.812245-120.163265-120.163266-120.163265S512 445.64898 512 512s53.812245 120.163265 120.163265 120.163265z\",\n                        fill: \"#51CDFF\",\n                        \"p-id\": \"6234\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 53,\n                        columnNumber: 1296\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined)\n        },\n        link: \"https://www.figma.com/education/\"\n    },\n    {\n        id: 3,\n        name: \"Canva for Education Schools\",\n        category: \"设计工具\",\n        description: \"免费使用Canva可画高级版，免费使用所有会员素材并无限制导出。使用@ghs.edu.kg邮箱登录后联系管理员获得。\",\n        icon: {\n            type: \"svg\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                t: \"1755831655991\",\n                class: \"icon\",\n                viewBox: \"0 0 1024 1024\",\n                version: \"1.1\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                \"p-id\": \"9147\",\n                width: \"200\",\n                height: \"200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M512 0C229.248 0 0 229.248 0 512s229.248 512 512 512 512-229.248 512-512S794.752 0 512 0zM297.045333 327.68c32.170667 0 57.045333 23.424 59.946667 51.2 2.944 24.874667-7.296 46.805333-35.072 59.989333-14.634667 7.296-20.48 7.338667-23.424 2.944-1.450667-2.944 0-5.845333 2.944-8.789333 26.325333-21.930667 26.325333-39.509333 23.381333-64.341333-1.450667-16.128-13.141333-26.368-24.874666-26.368-51.2 0-124.330667 114.090667-114.090667 197.504 4.394667 32.170667 23.424 70.229333 64.384 70.229333 13.141333 0 27.733333-4.394667 40.96-10.24 21.333333-11.264 34.090667-20.053333 46.805333-34.133333-3.114667-37.76 30.037333-87.296 78.976-87.296 21.973333 0 39.509333 8.746667 40.96 24.874666 2.901333 21.930667-16.085333 24.832-21.930666 24.832s-16.128-1.450667-16.128-7.253333c-1.450667-5.888 13.184-2.986667 11.733333-16.128-1.493333-8.789333-10.24-11.690667-19.029333-11.690667-30.72 0-48.256 42.410667-43.904 68.736 1.493333 11.733333 7.338667 23.424 19.072 23.424 8.746667 0 21.930667-13.226667 26.325333-32.213333 2.901333-13.141333 14.634667-21.930667 24.874667-21.930667 4.352 0 7.253333 1.450667 8.746666 7.296v5.888c-1.450667 5.845333-5.845333 23.381333-4.352 27.776 0 2.944 1.450667 7.296 7.253334 7.296 3.925333 0 18.602667-7.68 33.152-19.584 4.992-25.173333 10.794667-55.381333 10.794666-57.898666 1.450667-10.24 5.845333-20.48 26.325334-20.48 4.394667 0 7.296 1.450667 8.746666 7.296v5.888l-5.802666 26.325333c18.986667-24.874667 46.805333-42.410667 64.341333-42.410667 7.338667 0 13.184 4.352 13.184 11.690667 0 4.394667 0 11.690667-2.944 19.029333-5.845333 16.085333-13.184 40.96-17.578667 62.890667 0 5.845333 1.493333 11.690667 8.832 11.690667 7.296 0 29.226667-8.789333 46.762667-32.170667l0.298667-0.170667c-0.085333-2.901333-0.298667-5.717333-0.298667-8.618666 0-17.536 1.493333-32.170667 4.437333-42.410667 2.901333-11.690667 17.536-21.930667 26.325334-21.930667 4.394667 0 8.746667 2.944 8.746666 7.296 0 1.493333 0 4.394667-1.450666 5.845334-5.845333 19.029333-10.24 36.565333-10.24 54.144 0 10.24 1.450667 24.832 4.352 33.621333 0 1.450667 1.493333 2.944 2.986666 2.944 2.901333 0 23.381333-18.986667 37.973334-43.861333-13.141333-8.789333-20.48-23.424-20.48-40.96 0-30.72 19.029333-46.805333 36.608-46.805334 14.634667 0 26.325333 10.24 26.325333 30.72 0 13.141333-4.394667 27.733333-11.690667 40.96h4.352a32.853333 32.853333 0 0 0 24.917334-10.24 12.501333 12.501333 0 0 1 5.717333-4.992c14.293333-18.133333 35.413333-31.573333 60.16-31.573333 20.48 0 39.424 8.746667 40.917333 24.832 2.901333 21.973333-16.128 26.368-21.973333 26.368l-0.085333-0.085333c-5.888 0-16.085333-1.493333-16.085334-7.338667 0-5.845333 13.184-2.901333 11.690667-16.042667-1.450667-8.789333-10.24-11.733333-19.029333-11.733333-29.269333 0-48.213333 38.016-43.861334 68.736 1.450667 11.733333 7.296 24.874667 18.986667 24.874667 8.789333 0 21.973333-13.141333 27.818667-32.170667 2.901333-11.690667 14.634667-21.930667 24.874666-21.930667 4.394667 0 7.253333 1.450667 8.746667 7.296 0 2.944 0 8.789333-5.845333 27.818667-7.253333 13.141333-7.296 20.48-5.845334 26.325333 1.450667 11.690667 7.296 20.48 13.184 24.874667 1.450667 1.450667 2.901333 4.352 2.901334 4.352 0 2.944-1.450667 5.888-5.845334 5.888-1.450667 0-2.901333 0-4.394666-1.493333-21.930667-8.746667-30.72-23.381333-33.664-38.016-8.746667 10.24-18.986667 16.085333-30.72 16.085333-18.986667 0-37.973333-17.536-40.96-39.509333a68.650667 68.650667 0 0 1 3.2-27.690667c-8.661333 5.546667-18.005333 8.661333-26.581334 8.661333h-7.253333c-19.072 27.818667-39.552 46.848-54.186667 55.594667a38.229333 38.229333 0 0 1-16.085333 4.437333c-2.901333 0-7.296-1.493333-8.746667-4.437333-4.053333-6.485333-6.656-16.725333-8.234666-28.458667-20.522667 22.485333-48.853333 34.346667-61.994667 34.346667-14.634667 0-23.381333-8.789333-24.832-23.466667v-16.042666c4.352-32.170667 16.085333-51.2 16.085333-57.045334a3.157333 3.157333 0 0 0-2.944-2.986666c-10.24 0-43.861333 35.157333-49.749333 58.581333l-4.394667 18.986667c-2.901333 13.184-16.085333 21.973333-24.832 21.973333-4.394667 0-7.338667-1.493333-8.789333-7.338667v-5.845333l1.962667-9.941333c-18.56 13.226667-37.12 21.674667-45.866667 21.674666-13.141333 0-20.48-7.338667-21.930667-17.578666-8.789333 11.690667-18.986667 17.578667-32.170666 17.578666-15.018667 0-29.696-10.24-36.778667-25.301333-10.410667 11.733333-22.314667 23.594667-36.352 32.597333-20.48 13.184-43.861333 23.424-71.68 23.424-24.832 0-46.805333-13.184-58.496-24.874666-17.578667-16.085333-27.776-40.96-29.269333-64.384-8.746667-71.68 35.114667-163.84 102.4-204.8 16.128-8.746667 32.213333-14.634667 48.298666-14.634667z m416.853334 140.416c-4.437333 0-7.338667 7.338667-7.338667 14.634667 0 11.690667 5.845333 24.874667 13.184 32.213333a74.24 74.24 0 0 0 4.352-24.874667c0-14.634667-5.845333-21.973333-10.24-21.973333z\",\n                    \"p-id\": \"9148\",\n                    fill: \"#1296db\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                    lineNumber: 66,\n                    columnNumber: 155\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined)\n        },\n        link: \"https://www.canva.com/\"\n    },\n    {\n        id: 4,\n        name: \"Google Workspace\",\n        category: \"开发工具\",\n        description: \"Google Workspace全套权益，可使用Gemini，也可以去开aistudio里面的api key，相当于一个正常的Google账户。\",\n        icon: {\n            type: \"svg\",\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                t: \"1755832289464\",\n                class: \"icon\",\n                viewBox: \"0 0 1024 1024\",\n                version: \"1.1\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                \"p-id\": \"12100\",\n                width: \"200\",\n                height: \"200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M214.101333 512c0-32.512 5.546667-63.701333 15.36-92.928L57.173333 290.218667A491.861333 491.861333 0 0 0 4.693333 512c0 79.701333 18.858667 154.88 52.394667 221.610667l172.202667-129.066667A290.56 290.56 0 0 1 214.101333 512\",\n                        fill: \"#FBBC05\",\n                        \"p-id\": \"12101\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 79,\n                        columnNumber: 156\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M516.693333 216.192c72.106667 0 137.258667 25.002667 188.458667 65.962667L854.101333 136.533333C763.349333 59.178667 646.997333 11.392 516.693333 11.392c-202.325333 0-376.234667 113.28-459.52 278.826667l172.373334 128.853333c39.68-118.016 152.832-202.88 287.146666-202.88\",\n                        fill: \"#EA4335\",\n                        \"p-id\": \"12102\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 79,\n                        columnNumber: 427\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M516.693333 807.808c-134.357333 0-247.509333-84.864-287.232-202.88l-172.288 128.853333c83.242667 165.546667 257.152 278.826667 459.52 278.826667 124.842667 0 244.053333-43.392 333.568-124.757333l-163.584-123.818667c-46.122667 28.458667-104.234667 43.776-170.026666 43.776\",\n                        fill: \"#34A853\",\n                        \"p-id\": \"12103\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 79,\n                        columnNumber: 744\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M1005.397333 512c0-29.568-4.693333-61.44-11.648-91.008H516.650667V614.4h274.602666c-13.696 65.962667-51.072 116.650667-104.533333 149.632l163.541333 123.818667c93.994667-85.418667 155.136-212.650667 155.136-375.850667\",\n                        fill: \"#4285F4\",\n                        \"p-id\": \"12104\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                        lineNumber: 79,\n                        columnNumber: 1061\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\data\\\\student-benefits.js\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, undefined)\n        },\n        link: \"https://www.google.com/\"\n    }\n];\n// 权益分类配置\nconst benefitCategories = [\n    \"学习工具\",\n    \"开发工具\",\n    \"办公软件\",\n    \"创意设计\",\n    \"娱乐服务\",\n    \"设计工具\"\n];\n// 获取权益总数\nconst getBenefitsCount = ()=>studentBenefits.length;\n// 根据分类获取权益\nconst getBenefitsByCategory = (category)=>{\n    return studentBenefits.filter((benefit)=>benefit.category === category);\n};\n// 根据ID获取权益\nconst getBenefitById = (id)=>{\n    return studentBenefits.find((benefit)=>benefit.id === id);\n};\n// 获取所有分类\nconst getAllCategories = ()=>{\n    return [\n        ...new Set(studentBenefits.map((benefit)=>benefit.category))\n    ];\n};\n// 默认导出\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    studentBenefits,\n    benefitCategories,\n    getBenefitsCount,\n    getBenefitsByCategory,\n    getBenefitById,\n    getAllCategories\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./data/student-benefits.js\n");

/***/ }),

/***/ "./pages/student-portal.js":
/*!*********************************!*\
  !*** ./pages/student-portal.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentPortal),\n/* harmony export */   \"getServerSideProps\": () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _data_student_benefits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../data/student-benefits */ \"./data/student-benefits.js\");\n\n\n\n\n\n\n\nasync function fetchGoogleUser(email) {\n    // 刷新 token\n    const tokenRes = await fetch(\"https://oauth2.googleapis.com/token\", {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n            client_id: process.env.GOOGLE_CLIENT_ID,\n            client_secret: process.env.GOOGLE_CLIENT_SECRET,\n            refresh_token: process.env.GOOGLE_REFRESH_TOKEN,\n            grant_type: \"refresh_token\"\n        })\n    });\n    if (!tokenRes.ok) return null;\n    const { access_token  } = await tokenRes.json();\n    // 查询 Directory\n    const userRes = await fetch(`https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(email)}`, {\n        headers: {\n            Authorization: `Bearer ${access_token}`\n        }\n    });\n    if (!userRes.ok) return null;\n    return await userRes.json();\n}\nasync function getServerSideProps({ req  }) {\n    const cookies = (0,cookie__WEBPACK_IMPORTED_MODULE_3__.parse)(req.headers.cookie || \"\");\n    const userEmail = cookies.userEmail;\n    const userName = cookies.userName;\n    // 检查是否已登录（通过Google OAuth）\n    if (!userEmail || !userName) {\n        return {\n            redirect: {\n                destination: \"/login\",\n                permanent: false\n            }\n        };\n    }\n    // 验证邮箱域名\n    const allowedDomain = process.env.EMAIL_DOMAIN || \"ghs.edu.kg\";\n    const domain = allowedDomain.startsWith(\"@\") ? allowedDomain.slice(1) : allowedDomain;\n    if (!userEmail.endsWith(`@${domain}`)) {\n        return {\n            redirect: {\n                destination: \"/login?error=invalid_domain\",\n                permanent: false\n            }\n        };\n    }\n    // 获取Google Directory中的用户信息\n    const googleUser = await fetchGoogleUser(userEmail);\n    if (!googleUser) {\n        return {\n            redirect: {\n                destination: \"/login?error=user_not_found\",\n                permanent: false\n            }\n        };\n    }\n    // 从Google Directory获取用户信息\n    const fullName = `${googleUser.name.givenName} ${googleUser.name.familyName}`;\n    const personalEmail = googleUser.recoveryEmail || \"\";\n    // 从邮箱中提取用户名作为学生ID\n    const studentId = userEmail.split(\"@\")[0];\n    return {\n        props: {\n            fullName,\n            studentEmail: userEmail,\n            personalEmail,\n            studentId,\n            userPicture: cookies.userPicture || \"\"\n        }\n    };\n}\n// 图标渲染组件\nconst IconRenderer = ({ icon , className =\"\"  })=>{\n    if (icon.type === \"svg\") {\n        // 克隆SVG元素并强制设置统一尺寸\n        const svgElement = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4___default().cloneElement(icon.content, {\n            width: \"24\",\n            height: \"24\",\n            style: {\n                width: \"24px\",\n                height: \"24px\",\n                display: \"block\"\n            }\n        });\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: svgElement\n        }, void 0, false, {\n            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n            lineNumber: 81,\n            columnNumber: 12\n        }, undefined);\n    } else if (icon.type === \"image\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: className,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: icon.content,\n                alt: icon.alt || \"图标\",\n                style: {\n                    width: \"24px\",\n                    height: \"24px\",\n                    objectFit: \"contain\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined);\n    }\n    return null;\n};\nfunction StudentPortal({ fullName , studentEmail , personalEmail , studentId , userPicture  }) {\n    const handleLogout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n            window.location.href = \"/\";\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            // 即使出错也跳转到首页\n            window.location.href = \"/\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-aa166c2c111a521a\",\n                    children: \"学生门户 - Great Heights School\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-aa166c2c111a521a\" + \" \" + \"container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"header-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"logo\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/resources/logo.jpg\",\n                                            alt: \"Great Heights School Logo\",\n                                            className: \"jsx-aa166c2c111a521a\" + \" \" + \"logo-image\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-aa166c2c111a521a\" + \" \" + \"logo-text\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"school-name\",\n                                                    children: \"Great Heights School\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"portal-name\",\n                                                    children: \"Student Portal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"header-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"user-info\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-aa166c2c111a521a\" + \" \" + \"user-avatar\",\n                                            children: userPicture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: userPicture,\n                                                alt: \"Profile\",\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"avatar-image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"avatar-placeholder\",\n                                                children: fullName.split(\" \").map((name)=>name[0]).join(\"\").toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-aa166c2c111a521a\" + \" \" + \"user-details\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"user-name\",\n                                                    children: fullName\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"user-email\",\n                                                    children: studentEmail\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleLogout,\n                                            className: \"jsx-aa166c2c111a521a\" + \" \" + \"logout-btn\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    className: \"jsx-aa166c2c111a521a\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z\",\n                                                            fill: \"currentColor\",\n                                                            className: \"jsx-aa166c2c111a521a\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z\",\n                                                            fill: \"currentColor\",\n                                                            className: \"jsx-aa166c2c111a521a\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"登出\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"main-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"welcome-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-aa166c2c111a521a\",\n                                        children: [\n                                            \"欢迎回来, \",\n                                            fullName,\n                                            \"!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-aa166c2c111a521a\",\n                                        children: \"探索您的学生权益，充分利用教育资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"dashboard-grid\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"dashboard-card student-info-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-aa166c2c111a521a\",\n                                                        children: \"学生信息\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"jsx-aa166c2c111a521a\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\",\n                                                                fill: \"currentColor\",\n                                                                className: \"jsx-aa166c2c111a521a\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"label\",\n                                                                children: \"学生ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"value\",\n                                                                children: studentId\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"label\",\n                                                                children: \"姓名\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"value\",\n                                                                children: fullName\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"label\",\n                                                                children: \"学校邮箱\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"value\",\n                                                                children: studentEmail\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"info-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"label\",\n                                                                children: \"个人邮箱\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"value\",\n                                                                children: personalEmail || \"未设置\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"dashboard-card quick-actions-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-aa166c2c111a521a\",\n                                                        children: \"快速操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"jsx-aa166c2c111a521a\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M7 2v11h3v9l7-12h-4l4-8z\",\n                                                                fill: \"currentColor\",\n                                                                className: \"jsx-aa166c2c111a521a\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-content\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/reset-password\",\n                                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"action-button\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-aa166c2c111a521a\" + \" \" + \"action-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"20\",\n                                                                height: \"20\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"jsx-aa166c2c111a521a\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z\",\n                                                                    fill: \"currentColor\",\n                                                                    className: \"jsx-aa166c2c111a521a\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-aa166c2c111a521a\" + \" \" + \"action-text\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"action-title\",\n                                                                    children: \"重置密码\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-aa166c2c111a521a\" + \" \" + \"action-desc\",\n                                                                    children: \"修改您的账户密码\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"dashboard-card benefits-overview-card\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-aa166c2c111a521a\",\n                                                        children: \"学生权益概览\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-icon\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"24\",\n                                                            height: \"24\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"jsx-aa166c2c111a521a\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z\",\n                                                                fill: \"currentColor\",\n                                                                className: \"jsx-aa166c2c111a521a\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"card-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefits-stats\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"stat-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"stat-number\",\n                                                                        children: (0,_data_student_benefits__WEBPACK_IMPORTED_MODULE_5__.getBenefitsCount)()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                        lineNumber: 229,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"stat-label\",\n                                                                        children: \"可用权益\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"stat-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"stat-number\",\n                                                                        children: \"∞\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"stat-label\",\n                                                                        children: \"节省金额\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefits-desc\",\n                                                        children: \"作为 Great Heights School 的学生，您可以享受多项专属权益和优惠服务\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefits-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"section-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"jsx-aa166c2c111a521a\",\n                                                children: \"学生专享权益\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-aa166c2c111a521a\",\n                                                children: \"点击卡片了解更多详情并访问服务\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefits-grid\",\n                                        children: _data_student_benefits__WEBPACK_IMPORTED_MODULE_5__.studentBenefits.map((benefit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: benefit.link,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefit-card\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconRenderer, {\n                                                        icon: benefit.icon,\n                                                        className: \"benefit-icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefit-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"jsx-aa166c2c111a521a\",\n                                                                children: benefit.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-aa166c2c111a521a\",\n                                                                children: benefit.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefit-category\",\n                                                                children: benefit.category\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"benefit-arrow\",\n                                                        children: \"→\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, benefit.id, true, {\n                                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"jsx-aa166c2c111a521a\" + \" \" + \"footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-aa166c2c111a521a\",\n                                children: [\n                                    \"Powered by\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        target: \"_blank\",\n                                        rel: \"noopener\",\n                                        className: \"jsx-aa166c2c111a521a\",\n                                        children: \"Garbage Human Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"jsx-aa166c2c111a521a\",\n                                children: \"\\xa9 2025 Great Heights School. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\student-portal.js\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"aa166c2c111a521a\",\n                children: '.container.jsx-aa166c2c111a521a{min-height:100vh;background:#fafafa;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",\"Roboto\",\"Oxygen\",\"Ubuntu\",\"Cantarell\",\"Fira Sans\",\"Droid Sans\",\"Helvetica Neue\",sans-serif}.header.jsx-aa166c2c111a521a{background:#fff;padding:16px 32px;border-bottom:1px solid#e1e5e9;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;position:-webkit-sticky;position:sticky;top:0;z-index:100}.header-left.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.header-right.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.logo.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px}.logo-image.jsx-aa166c2c111a521a{width:32px;height:32px;-o-object-fit:contain;object-fit:contain;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.logo-text.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.school-name.jsx-aa166c2c111a521a{color:#111827;font-size:16px;font-weight:600;letter-spacing:-.02em;line-height:1.2}.portal-name.jsx-aa166c2c111a521a{color:#6b7280;font-size:13px;font-weight:400;line-height:1.2}.user-info.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px}.user-avatar.jsx-aa166c2c111a521a{width:32px;height:32px;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;overflow:hidden;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}.avatar-image.jsx-aa166c2c111a521a{width:100%;height:100%;-o-object-fit:cover;object-fit:cover}.avatar-placeholder.jsx-aa166c2c111a521a{width:100%;height:100%;background:#111827;color:#fff;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;font-size:12px;font-weight:600}.user-details.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;min-width:0}.user-name.jsx-aa166c2c111a521a{font-weight:600;color:#111827;font-size:14px;line-height:1.2;white-space:nowrap;overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis}.user-email.jsx-aa166c2c111a521a{font-size:12px;color:#6b7280;line-height:1.2;white-space:nowrap;overflow:hidden;-o-text-overflow:ellipsis;text-overflow:ellipsis}.logout-btn.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:6px;padding:6px 12px;background:#fff;color:#6b7280;border:1px solid#e1e5e9;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;font-size:13px;font-weight:500;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out;white-space:nowrap}.logout-btn.jsx-aa166c2c111a521a:hover{background:#f9fafb;color:#374151;border-color:#d1d5db}.main-content.jsx-aa166c2c111a521a{max-width:1200px;margin:0 auto;padding:32px 20px}.welcome-section.jsx-aa166c2c111a521a{text-align:center;margin-bottom:40px}.welcome-section.jsx-aa166c2c111a521a h2.jsx-aa166c2c111a521a{color:#111827;font-size:32px;font-weight:600;margin-bottom:8px;letter-spacing:-.02em}.welcome-section.jsx-aa166c2c111a521a p.jsx-aa166c2c111a521a{color:#6b7280;font-size:16px;margin:0}.dashboard-grid.jsx-aa166c2c111a521a{display:grid;grid-template-columns:repeat(auto-fit,minmax(320px,1fr));gap:24px;margin-bottom:48px}.dashboard-card.jsx-aa166c2c111a521a{background:#fff;border:1px solid#e1e5e9;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;padding:24px;-webkit-transition:all.2s ease-out;-moz-transition:all.2s ease-out;-o-transition:all.2s ease-out;transition:all.2s ease-out}.dashboard-card.jsx-aa166c2c111a521a:hover{border-color:#d1d5db;-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.08);-moz-box-shadow:0 4px 12px rgba(0,0,0,.08);box-shadow:0 4px 12px rgba(0,0,0,.08)}.card-header.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;margin-bottom:20px}.card-header.jsx-aa166c2c111a521a h3.jsx-aa166c2c111a521a{color:#111827;font-size:18px;font-weight:600;margin:0}.card-icon.jsx-aa166c2c111a521a{font-size:24px;opacity:.7;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.card-icon.jsx-aa166c2c111a521a svg.jsx-aa166c2c111a521a{width:24px!important;height:24px!important;max-width:24px;max-height:24px}.card-content.jsx-aa166c2c111a521a{color:#374151}.info-item.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:12px 0;border-bottom:1px solid#f3f4f6}.info-item.jsx-aa166c2c111a521a:last-child{border-bottom:none}.info-item.jsx-aa166c2c111a521a .label.jsx-aa166c2c111a521a{font-size:14px;color:#6b7280;font-weight:500}.info-item.jsx-aa166c2c111a521a .value.jsx-aa166c2c111a521a{font-size:14px;color:#111827;font-weight:500}.action-button.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:12px;padding:16px;background:#f9fafb;border:1px solid#e5e7eb;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;text-decoration:none;-webkit-transition:all.15s ease-out;-moz-transition:all.15s ease-out;-o-transition:all.15s ease-out;transition:all.15s ease-out}.action-button.jsx-aa166c2c111a521a:hover{background:#f3f4f6;border-color:#d1d5db;-webkit-transform:translatey(-1px);-moz-transform:translatey(-1px);-ms-transform:translatey(-1px);-o-transform:translatey(-1px);transform:translatey(-1px)}.action-icon.jsx-aa166c2c111a521a{font-size:20px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.action-icon.jsx-aa166c2c111a521a svg.jsx-aa166c2c111a521a{width:20px!important;height:20px!important;max-width:20px;max-height:20px}.action-text.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.action-title.jsx-aa166c2c111a521a{color:#111827;font-weight:600;font-size:14px}.action-desc.jsx-aa166c2c111a521a{color:#6b7280;font-size:13px}.benefits-stats.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:24px;margin-bottom:16px}.stat-item.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.stat-number.jsx-aa166c2c111a521a{font-size:24px;font-weight:700;color:#111827}.stat-label.jsx-aa166c2c111a521a{font-size:12px;color:#6b7280;text-transform:uppercase;letter-spacing:.05em}.benefits-desc.jsx-aa166c2c111a521a{color:#6b7280;font-size:14px;line-height:1.5;margin:0}.benefits-section.jsx-aa166c2c111a521a{margin-top:48px}.section-header.jsx-aa166c2c111a521a{text-align:center;margin-bottom:32px}.section-header.jsx-aa166c2c111a521a h3.jsx-aa166c2c111a521a{color:#111827;font-size:24px;font-weight:600;margin-bottom:8px;letter-spacing:-.02em}.section-header.jsx-aa166c2c111a521a p.jsx-aa166c2c111a521a{color:#6b7280;font-size:15px;margin:0}.benefits-grid.jsx-aa166c2c111a521a{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px}.benefit-card.jsx-aa166c2c111a521a{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;gap:16px;padding:20px;background:#fff;border:1px solid#e1e5e9;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;text-decoration:none;-webkit-transition:all.2s ease-out;-moz-transition:all.2s ease-out;-o-transition:all.2s ease-out;transition:all.2s ease-out}.benefit-card.jsx-aa166c2c111a521a:hover{border-color:#d1d5db;-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.08);-moz-box-shadow:0 4px 12px rgba(0,0,0,.08);box-shadow:0 4px 12px rgba(0,0,0,.08)}.benefit-icon.jsx-aa166c2c111a521a{font-size:32px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;width:32px;height:32px}.benefit-icon.jsx-aa166c2c111a521a img.jsx-aa166c2c111a521a{max-width:100%;max-height:100%;-o-object-fit:contain;object-fit:contain}.benefit-icon.jsx-aa166c2c111a521a svg.jsx-aa166c2c111a521a{width:24px!important;height:24px!important;max-width:24px;max-height:24px}.benefit-content.jsx-aa166c2c111a521a{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1}.benefit-content.jsx-aa166c2c111a521a h4.jsx-aa166c2c111a521a{color:#111827;font-size:16px;font-weight:600;margin:0 0 4px 0}.benefit-content.jsx-aa166c2c111a521a p.jsx-aa166c2c111a521a{color:#6b7280;font-size:14px;line-height:1.4;margin:0 0 8px 0}.benefit-category.jsx-aa166c2c111a521a{display:inline-block;background:#f3f4f6;color:#6b7280;font-size:12px;font-weight:500;padding:4px 8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;text-transform:uppercase;letter-spacing:.05em}.benefit-arrow.jsx-aa166c2c111a521a{color:#9ca3af;font-size:18px;font-weight:600;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-webkit-transition:all.2s ease-out;-moz-transition:all.2s ease-out;-o-transition:all.2s ease-out;transition:all.2s ease-out}.benefit-card.jsx-aa166c2c111a521a:hover .benefit-arrow.jsx-aa166c2c111a521a{color:#6b7280;-webkit-transform:translatex(4px);-moz-transform:translatex(4px);-ms-transform:translatex(4px);-o-transform:translatex(4px);transform:translatex(4px)}.footer.jsx-aa166c2c111a521a{background:#fff;padding:32px 20px;text-align:center;color:#9ca3af;font-size:13px;border-top:1px solid#e1e5e9;margin-top:64px}.footer.jsx-aa166c2c111a521a p.jsx-aa166c2c111a521a{margin:3px 0}.footer.jsx-aa166c2c111a521a a.jsx-aa166c2c111a521a{color:#6b7280;text-decoration:none;font-weight:500}.footer.jsx-aa166c2c111a521a a.jsx-aa166c2c111a521a:hover{color:#374151;text-decoration:underline}@media(max-width:768px){.header.jsx-aa166c2c111a521a{padding:12px 16px;-webkit-box-orient:horizontal;-webkit-box-direction:normal;-webkit-flex-direction:row;-moz-box-orient:horizontal;-moz-box-direction:normal;-ms-flex-direction:row;flex-direction:row;gap:12px}.logo-text.jsx-aa166c2c111a521a{display:none}.user-details.jsx-aa166c2c111a521a{display:none}.logout-btn.jsx-aa166c2c111a521a{padding:6px 10px;font-size:12px}.dashboard-grid.jsx-aa166c2c111a521a{grid-template-columns:1fr;gap:16px}.benefits-grid.jsx-aa166c2c111a521a{grid-template-columns:1fr;gap:16px}.main-content.jsx-aa166c2c111a521a{padding:24px 16px}.welcome-section.jsx-aa166c2c111a521a h2.jsx-aa166c2c111a521a{font-size:28px}.benefit-card.jsx-aa166c2c111a521a{padding:16px}}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9zdHVkZW50LXBvcnRhbC5qcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBNEI7QUFDRTtBQUNFO0FBQ1A7QUFDbUQ7QUFFNUUsZUFBZU0sZ0JBQWdCQyxLQUFLLEVBQUU7SUFDcEMsV0FBVztJQUNYLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSx1Q0FBdUM7UUFDbEVDLFFBQVE7UUFDUkMsU0FBUztZQUFFLGdCQUFnQjtRQUFvQztRQUMvREMsTUFBTSxJQUFJQyxnQkFBZ0I7WUFDeEJDLFdBQWVDLFFBQVFDLEdBQUcsQ0FBQ0MsZ0JBQWdCO1lBQzNDQyxlQUFlSCxRQUFRQyxHQUFHLENBQUNHLG9CQUFvQjtZQUMvQ0MsZUFBZUwsUUFBUUMsR0FBRyxDQUFDSyxvQkFBb0I7WUFDL0NDLFlBQWU7UUFDakI7SUFDRjtJQUNBLElBQUksQ0FBQ2QsU0FBU2UsRUFBRSxFQUFFLE9BQU8sSUFBSTtJQUM3QixNQUFNLEVBQUVDLGFBQVksRUFBRSxHQUFHLE1BQU1oQixTQUFTaUIsSUFBSTtJQUU1QyxlQUFlO0lBQ2YsTUFBTUMsVUFBVSxNQUFNakIsTUFDcEIsQ0FBQyxzREFBc0QsRUFBRWtCLG1CQUFtQnBCLE9BQU8sQ0FBQyxFQUNwRjtRQUFFSSxTQUFTO1lBQUVpQixlQUFlLENBQUMsT0FBTyxFQUFFSixhQUFhLENBQUM7UUFBQztJQUFFO0lBRXpELElBQUksQ0FBQ0UsUUFBUUgsRUFBRSxFQUFFLE9BQU8sSUFBSTtJQUM1QixPQUFPLE1BQU1HLFFBQVFELElBQUk7QUFDM0I7QUFFTyxlQUFlSSxtQkFBbUIsRUFBRUMsSUFBRyxFQUFFLEVBQUU7SUFDaEQsTUFBTUMsVUFBVTlCLDZDQUFLQSxDQUFDNkIsSUFBSW5CLE9BQU8sQ0FBQ3FCLE1BQU0sSUFBSTtJQUM1QyxNQUFNQyxZQUFZRixRQUFRRSxTQUFTO0lBQ25DLE1BQU1DLFdBQVdILFFBQVFHLFFBQVE7SUFFakMsMEJBQTBCO0lBQzFCLElBQUksQ0FBQ0QsYUFBYSxDQUFDQyxVQUFVO1FBQzNCLE9BQU87WUFBRUMsVUFBVTtnQkFBRUMsYUFBYTtnQkFBVUMsV0FBVyxLQUFLO1lBQUM7UUFBRTtJQUNqRSxDQUFDO0lBRUQsU0FBUztJQUNULE1BQU1DLGdCQUFnQnZCLFFBQVFDLEdBQUcsQ0FBQ3VCLFlBQVksSUFBSTtJQUNsRCxNQUFNQyxTQUFTRixjQUFjRyxVQUFVLENBQUMsT0FBT0gsY0FBY0ksS0FBSyxDQUFDLEtBQUtKLGFBQWE7SUFFckYsSUFBSSxDQUFDTCxVQUFVVSxRQUFRLENBQUMsQ0FBQyxDQUFDLEVBQUVILE9BQU8sQ0FBQyxHQUFHO1FBQ3JDLE9BQU87WUFBRUwsVUFBVTtnQkFBRUMsYUFBYTtnQkFBK0JDLFdBQVcsS0FBSztZQUFDO1FBQUU7SUFDdEYsQ0FBQztJQUVELDJCQUEyQjtJQUMzQixNQUFNTyxhQUFhLE1BQU10QyxnQkFBZ0IyQjtJQUN6QyxJQUFJLENBQUNXLFlBQVk7UUFDZixPQUFPO1lBQUVULFVBQVU7Z0JBQUVDLGFBQWE7Z0JBQStCQyxXQUFXLEtBQUs7WUFBQztRQUFFO0lBQ3RGLENBQUM7SUFFRCwwQkFBMEI7SUFDMUIsTUFBTVEsV0FBVyxDQUFDLEVBQUVELFdBQVdFLElBQUksQ0FBQ0MsU0FBUyxDQUFDLENBQUMsRUFBRUgsV0FBV0UsSUFBSSxDQUFDRSxVQUFVLENBQUMsQ0FBQztJQUM3RSxNQUFNQyxnQkFBZ0JMLFdBQVdNLGFBQWEsSUFBSTtJQUNsRCxrQkFBa0I7SUFDbEIsTUFBTUMsWUFBWWxCLFVBQVVtQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7SUFFekMsT0FBTztRQUNMQyxPQUFPO1lBQ0xSO1lBQ0FTLGNBQWNyQjtZQUNkZ0I7WUFDQUU7WUFDQUksYUFBYXhCLFFBQVF3QixXQUFXLElBQUk7UUFDdEM7SUFDRjtBQUNGLENBQUM7QUFFRCxTQUFTO0FBQ1QsTUFBTUMsZUFBZSxDQUFDLEVBQUVDLEtBQUksRUFBRUMsV0FBWSxHQUFFLEVBQUUsR0FBSztJQUNqRCxJQUFJRCxLQUFLRSxJQUFJLEtBQUssT0FBTztRQUN2QixtQkFBbUI7UUFDbkIsTUFBTUMsMkJBQWF6RCx5REFBa0IsQ0FBQ3NELEtBQUtLLE9BQU8sRUFBRTtZQUNsREMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLE9BQU87Z0JBQUVGLE9BQU87Z0JBQVFDLFFBQVE7Z0JBQVFFLFNBQVM7WUFBUTtRQUMzRDtRQUNBLHFCQUFPLDhEQUFDQztZQUFJVCxXQUFXQTtzQkFBWUU7Ozs7OztJQUNyQyxPQUFPLElBQUlILEtBQUtFLElBQUksS0FBSyxTQUFTO1FBQ2hDLHFCQUNFLDhEQUFDUTtZQUFJVCxXQUFXQTtzQkFDZCw0RUFBQ1U7Z0JBQ0NDLEtBQUtaLEtBQUtLLE9BQU87Z0JBQ2pCUSxLQUFLYixLQUFLYSxHQUFHLElBQUk7Z0JBQ2pCTCxPQUFPO29CQUFFRixPQUFPO29CQUFRQyxRQUFRO29CQUFRTyxXQUFXO2dCQUFVOzs7Ozs7Ozs7OztJQUlyRSxDQUFDO0lBQ0QsT0FBTyxJQUFJO0FBQ2I7QUFFZSxTQUFTQyxjQUFjLEVBQUUzQixTQUFRLEVBQUVTLGFBQVksRUFBRUwsY0FBYSxFQUFFRSxVQUFTLEVBQUVJLFlBQVcsRUFBRSxFQUFFO0lBQ3ZHLE1BQU1rQixlQUFlLFVBQVk7UUFDL0IsSUFBSTtZQUNGLE1BQU1oRSxNQUFNLG9CQUFvQjtnQkFBRUMsUUFBUTtZQUFPO1lBQ2pEZ0UsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekIsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CLGFBQWE7WUFDYkgsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7UUFDekI7SUFDRjtJQUlBLHFCQUNFOzswQkFDRSw4REFBQzVFLGtEQUFJQTswQkFDSCw0RUFBQytFOzs4QkFBTTs7Ozs7Ozs7Ozs7MEJBRVQsOERBQUNaOzBEQUFjOztrQ0FDYiw4REFBQ2E7a0VBQWlCOzswQ0FDaEIsOERBQUNiOzBFQUFjOzBDQUNiLDRFQUFDQTs4RUFBYzs7c0RBQ2IsOERBQUNDOzRDQUFJQyxLQUFJOzRDQUFzQkMsS0FBSTtzRkFBc0M7Ozs7OztzREFDekUsOERBQUNIO3NGQUFjOzs4REFDYiw4REFBQ2M7OEZBQWU7OERBQWM7Ozs7Ozs4REFDOUIsOERBQUNBOzhGQUFlOzhEQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJcEMsOERBQUNkOzBFQUFjOzBDQUNiLDRFQUFDQTs4RUFBYzs7c0RBQ2IsOERBQUNBO3NGQUFjO3NEQUNaWiw0QkFDQyw4REFBQ2E7Z0RBQUlDLEtBQUtkO2dEQUFhZSxLQUFJOzBGQUFvQjs7Ozs7cUVBRS9DLDhEQUFDSDswRkFBYzswREFDWnRCLFNBQVNPLEtBQUssQ0FBQyxLQUFLOEIsR0FBRyxDQUFDcEMsQ0FBQUEsT0FBUUEsSUFBSSxDQUFDLEVBQUUsRUFBRXFDLElBQUksQ0FBQyxJQUFJQyxXQUFXOzs7OztvREFFakU7Ozs7OztzREFFSCw4REFBQ2pCO3NGQUFjOzs4REFDYiw4REFBQ2M7OEZBQWU7OERBQWFwQzs7Ozs7OzhEQUM3Qiw4REFBQ29DOzhGQUFlOzhEQUFjM0I7Ozs7Ozs7Ozs7OztzREFFaEMsOERBQUMrQjs0Q0FBT0MsU0FBU2I7c0ZBQXdCOzs4REFDdkMsOERBQUNjO29EQUFJeEIsT0FBTTtvREFBS0MsUUFBTztvREFBS3dCLFNBQVE7b0RBQVlDLE1BQUs7b0RBQU9DLE9BQU07OztzRUFDaEUsOERBQUNDOzREQUFLQyxHQUFFOzREQUE0REgsTUFBSzs7Ozs7OztzRUFDekUsOERBQUNFOzREQUFLQyxHQUFFOzREQUF3REgsTUFBSzs7Ozs7Ozs7Ozs7OztnREFDakU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPZCw4REFBQ0k7a0VBQWU7OzBDQUVkLDhEQUFDMUI7MEVBQWM7O2tEQUNiLDhEQUFDMkI7Ozs0Q0FBRzs0Q0FBT2pEOzRDQUFTOzs7Ozs7O2tEQUNwQiw4REFBQ2tEOztrREFBRTs7Ozs7Ozs7Ozs7OzBDQUlMLDhEQUFDNUI7MEVBQWM7O2tEQUViLDhEQUFDQTtrRkFBYzs7MERBQ2IsOERBQUNBOzBGQUFjOztrRUFDYiw4REFBQzZCOztrRUFBRzs7Ozs7O2tFQUNKLDhEQUFDN0I7a0dBQWM7a0VBQ2IsNEVBQUNvQjs0REFBSXhCLE9BQU07NERBQUtDLFFBQU87NERBQUt3QixTQUFROzREQUFZQyxNQUFLOzREQUFPQyxPQUFNOztzRUFDaEUsNEVBQUNDO2dFQUFLQyxHQUFFO2dFQUFnSEgsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSW5JLDhEQUFDdEI7MEZBQWM7O2tFQUNiLDhEQUFDQTtrR0FBYzs7MEVBQ2IsOERBQUNjOzBHQUFlOzBFQUFROzs7Ozs7MEVBQ3hCLDhEQUFDQTswR0FBZTswRUFBUzlCOzs7Ozs7Ozs7Ozs7a0VBRTNCLDhEQUFDZ0I7a0dBQWM7OzBFQUNiLDhEQUFDYzswR0FBZTswRUFBUTs7Ozs7OzBFQUN4Qiw4REFBQ0E7MEdBQWU7MEVBQVNwQzs7Ozs7Ozs7Ozs7O2tFQUUzQiw4REFBQ3NCO2tHQUFjOzswRUFDYiw4REFBQ2M7MEdBQWU7MEVBQVE7Ozs7OzswRUFDeEIsOERBQUNBOzBHQUFlOzBFQUFTM0I7Ozs7Ozs7Ozs7OztrRUFFM0IsOERBQUNhO2tHQUFjOzswRUFDYiw4REFBQ2M7MEdBQWU7MEVBQVE7Ozs7OzswRUFDeEIsOERBQUNBOzBHQUFlOzBFQUFTaEMsaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTWhELDhEQUFDa0I7a0ZBQWM7OzBEQUNiLDhEQUFDQTswRkFBYzs7a0VBQ2IsOERBQUM2Qjs7a0VBQUc7Ozs7OztrRUFDSiw4REFBQzdCO2tHQUFjO2tFQUNiLDRFQUFDb0I7NERBQUl4QixPQUFNOzREQUFLQyxRQUFPOzREQUFLd0IsU0FBUTs0REFBWUMsTUFBSzs0REFBT0MsT0FBTTs7c0VBQ2hFLDRFQUFDQztnRUFBS0MsR0FBRTtnRUFBMkJILE1BQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUk5Qyw4REFBQ3RCOzBGQUFjOzBEQUNiLDRFQUFDOEI7b0RBQUVyQixNQUFLOzhGQUE0Qjs7c0VBQ2xDLDhEQUFDVDtzR0FBYztzRUFDYiw0RUFBQ29CO2dFQUFJeEIsT0FBTTtnRUFBS0MsUUFBTztnRUFBS3dCLFNBQVE7Z0VBQVlDLE1BQUs7Z0VBQU9DLE9BQU07OzBFQUNoRSw0RUFBQ0M7b0VBQUtDLEdBQUU7b0VBQW9PSCxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztzRUFHclAsOERBQUN0QjtzR0FBYzs7OEVBQ2IsOERBQUNjOzhHQUFlOzhFQUFlOzs7Ozs7OEVBQy9CLDhEQUFDQTs4R0FBZTs4RUFBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBT3RDLDhEQUFDZDtrRkFBYzs7MERBQ2IsOERBQUNBOzBGQUFjOztrRUFDYiw4REFBQzZCOztrRUFBRzs7Ozs7O2tFQUNKLDhEQUFDN0I7a0dBQWM7a0VBQ2IsNEVBQUNvQjs0REFBSXhCLE9BQU07NERBQUtDLFFBQU87NERBQUt3QixTQUFROzREQUFZQyxNQUFLOzREQUFPQyxPQUFNOztzRUFDaEUsNEVBQUNDO2dFQUFLQyxHQUFFO2dFQUE4RUgsTUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSWpHLDhEQUFDdEI7MEZBQWM7O2tFQUNiLDhEQUFDQTtrR0FBYzs7MEVBQ2IsOERBQUNBOzBHQUFjOztrRkFDYiw4REFBQ2M7a0hBQWU7a0ZBQWU1RSx3RUFBZ0JBOzs7Ozs7a0ZBQy9DLDhEQUFDNEU7a0hBQWU7a0ZBQWE7Ozs7Ozs7Ozs7OzswRUFFL0IsOERBQUNkOzBHQUFjOztrRkFDYiw4REFBQ2M7a0hBQWU7a0ZBQWM7Ozs7OztrRkFDOUIsOERBQUNBO2tIQUFlO2tGQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBR2pDLDhEQUFDYztrR0FBWTtrRUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRbkMsOERBQUM1QjswRUFBYzs7a0RBQ2IsOERBQUNBO2tGQUFjOzswREFDYiw4REFBQzZCOzswREFBRzs7Ozs7OzBEQUNKLDhEQUFDRDs7MERBQUU7Ozs7Ozs7Ozs7OztrREFFTCw4REFBQzVCO2tGQUFjO2tEQUNaL0QsdUVBQW1CLENBQUMsQ0FBQzhGLHdCQUNwQiw4REFBQ0Q7Z0RBRUNyQixNQUFNc0IsUUFBUUMsSUFBSTtnREFDbEJDLFFBQU87Z0RBQ1BDLEtBQUk7MEZBQ007O2tFQUVWLDhEQUFDN0M7d0RBQWFDLE1BQU15QyxRQUFRekMsSUFBSTt3REFBRUMsV0FBVTs7Ozs7O2tFQUM1Qyw4REFBQ1M7a0dBQWM7OzBFQUNiLDhEQUFDbUM7OzBFQUFJSixRQUFRcEQsSUFBSTs7Ozs7OzBFQUNqQiw4REFBQ2lEOzswRUFBR0csUUFBUUssV0FBVzs7Ozs7OzBFQUN2Qiw4REFBQ3RCOzBHQUFlOzBFQUFvQmlCLFFBQVFNLFFBQVE7Ozs7Ozs7Ozs7OztrRUFFdEQsOERBQUNyQztrR0FBYztrRUFBZ0I7Ozs7Ozs7K0NBWjFCK0IsUUFBUU8sRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FtQnpCLDhEQUFDQztrRUFBaUI7OzBDQUNoQiw4REFBQ1g7OztvQ0FBRTtvQ0FDVTtrREFDWCw4REFBQ0U7d0NBQUVyQixNQUFLO3dDQUFJd0IsUUFBTzt3Q0FBU0MsS0FBSTs7a0RBQVc7Ozs7Ozs7Ozs7OzswQ0FJN0MsOERBQUNOOzswQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBK2JiLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naHMtZWR1LXNpZ251cC8uL3BhZ2VzL3N0dWRlbnQtcG9ydGFsLmpzPzBiZDEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJ1xuaW1wb3J0IHsgcGFyc2UgfSBmcm9tICdjb29raWUnXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgc3R1ZGVudEJlbmVmaXRzLCBnZXRCZW5lZml0c0NvdW50IH0gZnJvbSAnLi4vZGF0YS9zdHVkZW50LWJlbmVmaXRzJ1xuXG5hc3luYyBmdW5jdGlvbiBmZXRjaEdvb2dsZVVzZXIoZW1haWwpIHtcbiAgLy8g5Yi35pawIHRva2VuXG4gIGNvbnN0IHRva2VuUmVzID0gYXdhaXQgZmV0Y2goJ2h0dHBzOi8vb2F1dGgyLmdvb2dsZWFwaXMuY29tL3Rva2VuJywge1xuICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQnIH0sXG4gICAgYm9keTogbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICBjbGllbnRfaWQ6ICAgICBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX0lELFxuICAgICAgY2xpZW50X3NlY3JldDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9TRUNSRVQsXG4gICAgICByZWZyZXNoX3Rva2VuOiBwcm9jZXNzLmVudi5HT09HTEVfUkVGUkVTSF9UT0tFTixcbiAgICAgIGdyYW50X3R5cGU6ICAgICdyZWZyZXNoX3Rva2VuJyxcbiAgICB9KSxcbiAgfSlcbiAgaWYgKCF0b2tlblJlcy5vaykgcmV0dXJuIG51bGxcbiAgY29uc3QgeyBhY2Nlc3NfdG9rZW4gfSA9IGF3YWl0IHRva2VuUmVzLmpzb24oKVxuICBcbiAgLy8g5p+l6K+iIERpcmVjdG9yeVxuICBjb25zdCB1c2VyUmVzID0gYXdhaXQgZmV0Y2goXG4gICAgYGh0dHBzOi8vYWRtaW4uZ29vZ2xlYXBpcy5jb20vYWRtaW4vZGlyZWN0b3J5L3YxL3VzZXJzLyR7ZW5jb2RlVVJJQ29tcG9uZW50KGVtYWlsKX1gLFxuICAgIHsgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YWNjZXNzX3Rva2VufWAgfSB9XG4gIClcbiAgaWYgKCF1c2VyUmVzLm9rKSByZXR1cm4gbnVsbFxuICByZXR1cm4gYXdhaXQgdXNlclJlcy5qc29uKClcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFNlcnZlclNpZGVQcm9wcyh7IHJlcSB9KSB7XG4gIGNvbnN0IGNvb2tpZXMgPSBwYXJzZShyZXEuaGVhZGVycy5jb29raWUgfHwgJycpXG4gIGNvbnN0IHVzZXJFbWFpbCA9IGNvb2tpZXMudXNlckVtYWlsXG4gIGNvbnN0IHVzZXJOYW1lID0gY29va2llcy51c2VyTmFtZVxuXG4gIC8vIOajgOafpeaYr+WQpuW3sueZu+W9le+8iOmAmui/h0dvb2dsZSBPQXV0aO+8iVxuICBpZiAoIXVzZXJFbWFpbCB8fCAhdXNlck5hbWUpIHtcbiAgICByZXR1cm4geyByZWRpcmVjdDogeyBkZXN0aW5hdGlvbjogJy9sb2dpbicsIHBlcm1hbmVudDogZmFsc2UgfSB9XG4gIH1cblxuICAvLyDpqozor4Hpgq7nrrHln5/lkI1cbiAgY29uc3QgYWxsb3dlZERvbWFpbiA9IHByb2Nlc3MuZW52LkVNQUlMX0RPTUFJTiB8fCAnZ2hzLmVkdS5rZydcbiAgY29uc3QgZG9tYWluID0gYWxsb3dlZERvbWFpbi5zdGFydHNXaXRoKCdAJykgPyBhbGxvd2VkRG9tYWluLnNsaWNlKDEpIDogYWxsb3dlZERvbWFpblxuICBcbiAgaWYgKCF1c2VyRW1haWwuZW5kc1dpdGgoYEAke2RvbWFpbn1gKSkge1xuICAgIHJldHVybiB7IHJlZGlyZWN0OiB7IGRlc3RpbmF0aW9uOiAnL2xvZ2luP2Vycm9yPWludmFsaWRfZG9tYWluJywgcGVybWFuZW50OiBmYWxzZSB9IH1cbiAgfVxuXG4gIC8vIOiOt+WPlkdvb2dsZSBEaXJlY3RvcnnkuK3nmoTnlKjmiLfkv6Hmga9cbiAgY29uc3QgZ29vZ2xlVXNlciA9IGF3YWl0IGZldGNoR29vZ2xlVXNlcih1c2VyRW1haWwpXG4gIGlmICghZ29vZ2xlVXNlcikge1xuICAgIHJldHVybiB7IHJlZGlyZWN0OiB7IGRlc3RpbmF0aW9uOiAnL2xvZ2luP2Vycm9yPXVzZXJfbm90X2ZvdW5kJywgcGVybWFuZW50OiBmYWxzZSB9IH1cbiAgfVxuXG4gIC8vIOS7jkdvb2dsZSBEaXJlY3Rvcnnojrflj5bnlKjmiLfkv6Hmga9cbiAgY29uc3QgZnVsbE5hbWUgPSBgJHtnb29nbGVVc2VyLm5hbWUuZ2l2ZW5OYW1lfSAke2dvb2dsZVVzZXIubmFtZS5mYW1pbHlOYW1lfWBcbiAgY29uc3QgcGVyc29uYWxFbWFpbCA9IGdvb2dsZVVzZXIucmVjb3ZlcnlFbWFpbCB8fCAnJ1xuICAvLyDku47pgq7nrrHkuK3mj5Dlj5bnlKjmiLflkI3kvZzkuLrlrabnlJ9JRFxuICBjb25zdCBzdHVkZW50SWQgPSB1c2VyRW1haWwuc3BsaXQoJ0AnKVswXVxuXG4gIHJldHVybiB7XG4gICAgcHJvcHM6IHtcbiAgICAgIGZ1bGxOYW1lLFxuICAgICAgc3R1ZGVudEVtYWlsOiB1c2VyRW1haWwsXG4gICAgICBwZXJzb25hbEVtYWlsLFxuICAgICAgc3R1ZGVudElkLFxuICAgICAgdXNlclBpY3R1cmU6IGNvb2tpZXMudXNlclBpY3R1cmUgfHwgJydcbiAgICB9XG4gIH1cbn1cblxuLy8g5Zu+5qCH5riy5p+T57uE5Lu2XG5jb25zdCBJY29uUmVuZGVyZXIgPSAoeyBpY29uLCBjbGFzc05hbWUgPSAnJyB9KSA9PiB7XG4gIGlmIChpY29uLnR5cGUgPT09ICdzdmcnKSB7XG4gICAgLy8g5YWL6ZqGU1ZH5YWD57Sg5bm25by65Yi26K6+572u57uf5LiA5bC65a+4XG4gICAgY29uc3Qgc3ZnRWxlbWVudCA9IFJlYWN0LmNsb25lRWxlbWVudChpY29uLmNvbnRlbnQsIHtcbiAgICAgIHdpZHRoOiAnMjQnLFxuICAgICAgaGVpZ2h0OiAnMjQnLFxuICAgICAgc3R5bGU6IHsgd2lkdGg6ICcyNHB4JywgaGVpZ2h0OiAnMjRweCcsIGRpc3BsYXk6ICdibG9jaycgfVxuICAgIH0pXG4gICAgcmV0dXJuIDxkaXYgY2xhc3NOYW1lPXtjbGFzc05hbWV9PntzdmdFbGVtZW50fTwvZGl2PlxuICB9IGVsc2UgaWYgKGljb24udHlwZSA9PT0gJ2ltYWdlJykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgICAgPGltZ1xuICAgICAgICAgIHNyYz17aWNvbi5jb250ZW50fVxuICAgICAgICAgIGFsdD17aWNvbi5hbHQgfHwgJ+Wbvuaghyd9XG4gICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcyNHB4JywgaGVpZ2h0OiAnMjRweCcsIG9iamVjdEZpdDogJ2NvbnRhaW4nIH19XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cbiAgcmV0dXJuIG51bGxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3R1ZGVudFBvcnRhbCh7IGZ1bGxOYW1lLCBzdHVkZW50RW1haWwsIHBlcnNvbmFsRW1haWwsIHN0dWRlbnRJZCwgdXNlclBpY3R1cmUgfSkge1xuICBjb25zdCBoYW5kbGVMb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9nb3V0JywgeyBtZXRob2Q6ICdQT1NUJyB9KVxuICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnLydcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTG9nb3V0IGVycm9yOicsIGVycm9yKVxuICAgICAgLy8g5Y2z5L2/5Ye66ZSZ5Lmf6Lez6L2s5Yiw6aaW6aG1XG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvJ1xuICAgIH1cbiAgfVxuXG5cblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPHRpdGxlPuWtpueUn+mXqOaItyAtIEdyZWF0IEhlaWdodHMgU2Nob29sPC90aXRsZT5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiaGVhZGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZWFkZXItbGVmdFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2dvXCI+XG4gICAgICAgICAgICAgIDxpbWcgc3JjPVwiL3Jlc291cmNlcy9sb2dvLmpwZ1wiIGFsdD1cIkdyZWF0IEhlaWdodHMgU2Nob29sIExvZ29cIiBjbGFzc05hbWU9XCJsb2dvLWltYWdlXCIgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2dvLXRleHRcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzY2hvb2wtbmFtZVwiPkdyZWF0IEhlaWdodHMgU2Nob29sPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInBvcnRhbC1uYW1lXCI+U3R1ZGVudCBQb3J0YWw8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZWFkZXItcmlnaHRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidXNlci1pbmZvXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidXNlci1hdmF0YXJcIj5cbiAgICAgICAgICAgICAgICB7dXNlclBpY3R1cmUgPyAoXG4gICAgICAgICAgICAgICAgICA8aW1nIHNyYz17dXNlclBpY3R1cmV9IGFsdD1cIlByb2ZpbGVcIiBjbGFzc05hbWU9XCJhdmF0YXItaW1hZ2VcIiAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF2YXRhci1wbGFjZWhvbGRlclwiPlxuICAgICAgICAgICAgICAgICAgICB7ZnVsbE5hbWUuc3BsaXQoJyAnKS5tYXAobmFtZSA9PiBuYW1lWzBdKS5qb2luKCcnKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidXNlci1kZXRhaWxzXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidXNlci1uYW1lXCI+e2Z1bGxOYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ1c2VyLWVtYWlsXCI+e3N0dWRlbnRFbWFpbH08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUxvZ291dH0gY2xhc3NOYW1lPVwibG9nb3V0LWJ0blwiPlxuICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCIxNlwiIGhlaWdodD1cIjE2XCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xNyA3bC0xLjQxIDEuNDFMMTguMTcgMTFIOHYyaDEwLjE3bC0yLjU4IDIuNTlMMTcgMTdsNS01elwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIi8+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTQgNWg4VjNINGMtMS4xIDAtMiAuOS0yIDJ2MTRjMCAxLjEuOSAyIDIgMmg4di0ySDRWNXpcIiBmaWxsPVwiY3VycmVudENvbG9yXCIvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIOeZu+WHulxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2hlYWRlcj5cblxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9XCJtYWluLWNvbnRlbnRcIj5cbiAgICAgICAgICB7Lyog5qyi6L+O5Yy65Z+fICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid2VsY29tZS1zZWN0aW9uXCI+XG4gICAgICAgICAgICA8aDI+5qyi6L+O5Zue5p2lLCB7ZnVsbE5hbWV9ITwvaDI+XG4gICAgICAgICAgICA8cD7mjqLntKLmgqjnmoTlrabnlJ/mnYPnm4rvvIzlhYXliIbliKnnlKjmlZnogrLotYTmupA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Luq6KGo55uY572R5qC85biD5bGAICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGFzaGJvYXJkLWdyaWRcIj5cbiAgICAgICAgICAgIHsvKiDlrabnlJ/kv6Hmga/ljaHniYcgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRhc2hib2FyZC1jYXJkIHN0dWRlbnQtaW5mby1jYXJkXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgICA8aDM+5a2m55Sf5L+h5oGvPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtaWNvblwiPlxuICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD1cIjI0XCIgaGVpZ2h0PVwiMjRcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTIgMTJjMi4yMSAwIDQtMS43OSA0LTRzLTEuNzktNC00LTQtNCAxLjc5LTQgNCAxLjc5IDQgNCA0em0wIDJjLTIuNjcgMC04IDEuMzQtOCA0djJoMTZ2LTJjMC0yLjY2LTUuMzMtNC04LTR6XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImluZm8taXRlbVwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibGFiZWxcIj7lrabnlJ9JRDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInZhbHVlXCI+e3N0dWRlbnRJZH08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmZvLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImxhYmVsXCI+5aeT5ZCNPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidmFsdWVcIj57ZnVsbE5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5mby1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJsYWJlbFwiPuWtpuagoemCrueusTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInZhbHVlXCI+e3N0dWRlbnRFbWFpbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmZvLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImxhYmVsXCI+5Liq5Lq66YKu566xPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidmFsdWVcIj57cGVyc29uYWxFbWFpbCB8fCAn5pyq6K6+572uJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDlv6vpgJ/mk43kvZzljaHniYcgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRhc2hib2FyZC1jYXJkIHF1aWNrLWFjdGlvbnMtY2FyZFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtaGVhZGVyXCI+XG4gICAgICAgICAgICAgICAgPGgzPuW/q+mAn+aTjeS9nDwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWljb25cIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCIyNFwiIGhlaWdodD1cIjI0XCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTcgMnYxMWgzdjlsNy0xMmgtNGw0LTh6XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiL3Jlc2V0LXBhc3N3b3JkXCIgY2xhc3NOYW1lPVwiYWN0aW9uLWJ1dHRvblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhY3Rpb24taWNvblwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMjBcIiBoZWlnaHQ9XCIyMFwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBmaWxsPVwibm9uZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTE4IDhoLTFWNmMwLTIuNzYtMi4yNC01LTUtNVM3IDMuMjQgNyA2djJINmMtMS4xIDAtMiAuOS0yIDJ2MTBjMCAxLjEuOSAyIDIgMmgxMmMxLjEgMCAyLS45IDItMlYxMGMwLTEuMS0uOS0yLTItMnptLTYgOWMtMS4xIDAtMi0uOS0yLTJzLjktMiAyLTIgMiAuOSAyIDItLjkgMi0yIDJ6bTMuMS05SDguOVY2YzAtMS43MSAxLjM5LTMuMSAzLjEtMy4xIDEuNzEgMCAzLjEgMS4zOSAzLjEgMy4xdjJ6XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWN0aW9uLXRleHRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWN0aW9uLXRpdGxlXCI+6YeN572u5a+G56CBPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJhY3Rpb24tZGVzY1wiPuS/ruaUueaCqOeahOi0puaIt+WvhueggTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOWtpueUn+adg+ebiuWxleekuuWMuuWfnyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGFzaGJvYXJkLWNhcmQgYmVuZWZpdHMtb3ZlcnZpZXctY2FyZFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtaGVhZGVyXCI+XG4gICAgICAgICAgICAgICAgPGgzPuWtpueUn+adg+ebiuamguiniDwvaDM+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWljb25cIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCIyNFwiIGhlaWdodD1cIjI0XCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTUgMTMuMTh2NEwxMiAyMWw3LTMuODJ2LTRMMTIgMTdsLTctMy44MnpNMTIgM0wxIDlsMTEgNiA5LTQuOTFWMTdoMlY5TDEyIDN6XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJlbmVmaXRzLXN0YXRzXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXQtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzdGF0LW51bWJlclwiPntnZXRCZW5lZml0c0NvdW50KCl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzdGF0LWxhYmVsXCI+5Y+v55So5p2D55uKPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInN0YXQtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzdGF0LW51bWJlclwiPuKInjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3RhdC1sYWJlbFwiPuiKguecgemHkeminTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImJlbmVmaXRzLWRlc2NcIj5cbiAgICAgICAgICAgICAgICAgIOS9nOS4uiBHcmVhdCBIZWlnaHRzIFNjaG9vbCDnmoTlrabnlJ/vvIzmgqjlj6/ku6Xkuqvlj5flpJrpobnkuJPlsZ7mnYPnm4rlkozkvJjmg6DmnI3liqFcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5a2m55Sf5p2D55uK6K+m57uG5bGV56S6ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmVuZWZpdHMtc2VjdGlvblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWN0aW9uLWhlYWRlclwiPlxuICAgICAgICAgICAgICA8aDM+5a2m55Sf5LiT5Lqr5p2D55uKPC9oMz5cbiAgICAgICAgICAgICAgPHA+54K55Ye75Y2h54mH5LqG6Kej5pu05aSa6K+m5oOF5bm26K6/6Zeu5pyN5YqhPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJlbmVmaXRzLWdyaWRcIj5cbiAgICAgICAgICAgICAge3N0dWRlbnRCZW5lZml0cy5tYXAoKGJlbmVmaXQpID0+IChcbiAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAga2V5PXtiZW5lZml0LmlkfVxuICAgICAgICAgICAgICAgICAgaHJlZj17YmVuZWZpdC5saW5rfVxuICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmVuZWZpdC1jYXJkXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8SWNvblJlbmRlcmVyIGljb249e2JlbmVmaXQuaWNvbn0gY2xhc3NOYW1lPVwiYmVuZWZpdC1pY29uXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmVuZWZpdC1jb250ZW50XCI+XG4gICAgICAgICAgICAgICAgICAgIDxoND57YmVuZWZpdC5uYW1lfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDxwPntiZW5lZml0LmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmVuZWZpdC1jYXRlZ29yeVwiPntiZW5lZml0LmNhdGVnb3J5fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZW5lZml0LWFycm93XCI+4oaSPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21haW4+XG5cbiAgICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJmb290ZXJcIj5cbiAgICAgICAgICA8cD5cbiAgICAgICAgICAgIFBvd2VyZWQgYnl7JyAnfVxuICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub29wZW5lclwiPlxuICAgICAgICAgICAgICBHYXJiYWdlIEh1bWFuIFN0dWRpb1xuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8cD7CqSAyMDI1IEdyZWF0IEhlaWdodHMgU2Nob29sLiBBbGwgcmlnaHRzIHJlc2VydmVkLjwvcD5cbiAgICAgICAgPC9mb290ZXI+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPHN0eWxlIGpzeD57YFxuICAgICAgICAuY29udGFpbmVyIHtcbiAgICAgICAgICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmFmYWZhO1xuICAgICAgICAgIGZvbnQtZmFtaWx5OiAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsICdTZWdvZSBVSScsICdSb2JvdG8nLCAnT3h5Z2VuJywgJ1VidW50dScsICdDYW50YXJlbGwnLCAnRmlyYSBTYW5zJywgJ0Ryb2lkIFNhbnMnLCAnSGVsdmV0aWNhIE5ldWUnLCBzYW5zLXNlcmlmO1xuICAgICAgICB9XG4gICAgICAgIC5oZWFkZXIge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gICAgICAgICAgcGFkZGluZzogMTZweCAzMnB4O1xuICAgICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTFlNWU5O1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgcG9zaXRpb246IHN0aWNreTtcbiAgICAgICAgICB0b3A6IDA7XG4gICAgICAgICAgei1pbmRleDogMTAwO1xuICAgICAgICB9XG4gICAgICAgIC5oZWFkZXItbGVmdCB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICB9XG4gICAgICAgIC5oZWFkZXItcmlnaHQge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgfVxuICAgICAgICAubG9nbyB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGdhcDogMTJweDtcbiAgICAgICAgfVxuICAgICAgICAubG9nby1pbWFnZSB7XG4gICAgICAgICAgd2lkdGg6IDMycHg7XG4gICAgICAgICAgaGVpZ2h0OiAzMnB4O1xuICAgICAgICAgIG9iamVjdC1maXQ6IGNvbnRhaW47XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgICB9XG4gICAgICAgIC5sb2dvLXRleHQge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgICAgfVxuICAgICAgICAuc2Nob29sLW5hbWUge1xuICAgICAgICAgIGNvbG9yOiAjMTExODI3O1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAtMC4wMmVtO1xuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICAgIH1cbiAgICAgICAgLnBvcnRhbC1uYW1lIHtcbiAgICAgICAgICBjb2xvcjogIzZiNzI4MDtcbiAgICAgICAgICBmb250LXNpemU6IDEzcHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcbiAgICAgICAgICBsaW5lLWhlaWdodDogMS4yO1xuICAgICAgICB9XG4gICAgICAgIC51c2VyLWluZm8ge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBnYXA6IDEycHg7XG4gICAgICAgIH1cbiAgICAgICAgLnVzZXItYXZhdGFyIHtcbiAgICAgICAgICB3aWR0aDogMzJweDtcbiAgICAgICAgICBoZWlnaHQ6IDMycHg7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICAgIH1cbiAgICAgICAgLmF2YXRhci1pbWFnZSB7XG4gICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgICAgICB9XG4gICAgICAgIC5hdmF0YXItcGxhY2Vob2xkZXIge1xuICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjMTExODI3O1xuICAgICAgICAgIGNvbG9yOiAjZmZmZmZmO1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgfVxuICAgICAgICAudXNlci1kZXRhaWxzIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgbWluLXdpZHRoOiAwO1xuICAgICAgICB9XG4gICAgICAgIC51c2VyLW5hbWUge1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgY29sb3I6ICMxMTE4Mjc7XG4gICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7XG4gICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzO1xuICAgICAgICB9XG4gICAgICAgIC51c2VyLWVtYWlsIHtcbiAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgY29sb3I6ICM2YjcyODA7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjtcbiAgICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwO1xuICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7XG4gICAgICAgIH1cbiAgICAgICAgLmxvZ291dC1idG4ge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBnYXA6IDZweDtcbiAgICAgICAgICBwYWRkaW5nOiA2cHggMTJweDtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMWU1ZTk7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgICBmb250LXNpemU6IDEzcHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4xNXMgZWFzZS1vdXQ7XG4gICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDtcbiAgICAgICAgfVxuICAgICAgICAubG9nb3V0LWJ0bjpob3ZlciB7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2Y5ZmFmYjtcbiAgICAgICAgICBjb2xvcjogIzM3NDE1MTtcbiAgICAgICAgICBib3JkZXItY29sb3I6ICNkMWQ1ZGI7XG4gICAgICAgIH1cbiAgICAgICAgLm1haW4tY29udGVudCB7XG4gICAgICAgICAgbWF4LXdpZHRoOiAxMjAwcHg7XG4gICAgICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgICAgICAgcGFkZGluZzogMzJweCAyMHB4O1xuICAgICAgICB9XG4gICAgICAgIC53ZWxjb21lLXNlY3Rpb24ge1xuICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0MHB4O1xuICAgICAgICB9XG4gICAgICAgIC53ZWxjb21lLXNlY3Rpb24gaDIge1xuICAgICAgICAgIGNvbG9yOiAjMTExODI3O1xuICAgICAgICAgIGZvbnQtc2l6ZTogMzJweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICAgICAgICBsZXR0ZXItc3BhY2luZzogLTAuMDJlbTtcbiAgICAgICAgfVxuICAgICAgICAud2VsY29tZS1zZWN0aW9uIHAge1xuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgIH1cbiAgICAgICAgLyog5Luq6KGo55uY572R5qC85biD5bGAICovXG4gICAgICAgIC5kYXNoYm9hcmQtZ3JpZCB7XG4gICAgICAgICAgZGlzcGxheTogZ3JpZDtcbiAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMyMHB4LCAxZnIpKTtcbiAgICAgICAgICBnYXA6IDI0cHg7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogNDhweDtcbiAgICAgICAgfVxuICAgICAgICAuZGFzaGJvYXJkLWNhcmQge1xuICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7XG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UxZTVlOTtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgICAgIHBhZGRpbmc6IDI0cHg7XG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1vdXQ7XG4gICAgICAgIH1cbiAgICAgICAgLmRhc2hib2FyZC1jYXJkOmhvdmVyIHtcbiAgICAgICAgICBib3JkZXItY29sb3I6ICNkMWQ1ZGI7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcbiAgICAgICAgfVxuICAgICAgICAuY2FyZC1oZWFkZXIge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICAgICAgfVxuICAgICAgICAuY2FyZC1oZWFkZXIgaDMge1xuICAgICAgICAgIGNvbG9yOiAjMTExODI3O1xuICAgICAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgfVxuICAgICAgICAuY2FyZC1pY29uIHtcbiAgICAgICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICAgICAgb3BhY2l0eTogMC43O1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgfVxuICAgICAgICAuY2FyZC1pY29uIHN2ZyB7XG4gICAgICAgICAgd2lkdGg6IDI0cHggIWltcG9ydGFudDtcbiAgICAgICAgICBoZWlnaHQ6IDI0cHggIWltcG9ydGFudDtcbiAgICAgICAgICBtYXgtd2lkdGg6IDI0cHg7XG4gICAgICAgICAgbWF4LWhlaWdodDogMjRweDtcbiAgICAgICAgfVxuICAgICAgICAuY2FyZC1jb250ZW50IHtcbiAgICAgICAgICBjb2xvcjogIzM3NDE1MTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8qIOWtpueUn+S/oeaBr+WNoeeJhyAqL1xuICAgICAgICAuaW5mby1pdGVtIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIHBhZGRpbmc6IDEycHggMDtcbiAgICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YzZjRmNjtcbiAgICAgICAgfVxuICAgICAgICAuaW5mby1pdGVtOmxhc3QtY2hpbGQge1xuICAgICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7XG4gICAgICAgIH1cbiAgICAgICAgLmluZm8taXRlbSAubGFiZWwge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgICBjb2xvcjogIzZiNzI4MDtcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICB9XG4gICAgICAgIC5pbmZvLWl0ZW0gLnZhbHVlIHtcbiAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgY29sb3I6ICMxMTE4Mjc7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgfVxuXG4gICAgICAgIC8qIOW/q+mAn+aTjeS9nOWNoeeJhyAqL1xuICAgICAgICAuYWN0aW9uLWJ1dHRvbiB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICAgIGdhcDogMTJweDtcbiAgICAgICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgICAgIGJhY2tncm91bmQ6ICNmOWZhZmI7XG4gICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2U1ZTdlYjtcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjE1cyBlYXNlLW91dDtcbiAgICAgICAgfVxuICAgICAgICAuYWN0aW9uLWJ1dHRvbjpob3ZlciB7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2YzZjRmNjtcbiAgICAgICAgICBib3JkZXItY29sb3I6ICNkMWQ1ZGI7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpO1xuICAgICAgICB9XG4gICAgICAgIC5hY3Rpb24taWNvbiB7XG4gICAgICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgfVxuICAgICAgICAuYWN0aW9uLWljb24gc3ZnIHtcbiAgICAgICAgICB3aWR0aDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgICAgIGhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgICAgIG1heC13aWR0aDogMjBweDtcbiAgICAgICAgICBtYXgtaGVpZ2h0OiAyMHB4O1xuICAgICAgICB9XG4gICAgICAgIC5hY3Rpb24tdGV4dCB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgICB9XG4gICAgICAgIC5hY3Rpb24tdGl0bGUge1xuICAgICAgICAgIGNvbG9yOiAjMTExODI3O1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xuICAgICAgICB9XG4gICAgICAgIC5hY3Rpb24tZGVzYyB7XG4gICAgICAgICAgY29sb3I6ICM2YjcyODA7XG4gICAgICAgICAgZm9udC1zaXplOiAxM3B4O1xuICAgICAgICB9XG5cbiAgICAgICAgLyog5p2D55uK5qaC6KeI5Y2h54mHICovXG4gICAgICAgIC5iZW5lZml0cy1zdGF0cyB7XG4gICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICBnYXA6IDI0cHg7XG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICAgICAgfVxuICAgICAgICAuc3RhdC1pdGVtIHtcbiAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgfVxuICAgICAgICAuc3RhdC1udW1iZXIge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgICAgIGNvbG9yOiAjMTExODI3O1xuICAgICAgICB9XG4gICAgICAgIC5zdGF0LWxhYmVsIHtcbiAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgY29sb3I6ICM2YjcyODA7XG4gICAgICAgICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbiAgICAgICAgICBsZXR0ZXItc3BhY2luZzogMC4wNWVtO1xuICAgICAgICB9XG4gICAgICAgIC5iZW5lZml0cy1kZXNjIHtcbiAgICAgICAgICBjb2xvcjogIzZiNzI4MDtcbiAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNTtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgIH1cbiAgICAgICAgLyog5a2m55Sf5p2D55uK5bGV56S65Yy65Z+fICovXG4gICAgICAgIC5iZW5lZml0cy1zZWN0aW9uIHtcbiAgICAgICAgICBtYXJnaW4tdG9wOiA0OHB4O1xuICAgICAgICB9XG4gICAgICAgIC5zZWN0aW9uLWhlYWRlciB7XG4gICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDMycHg7XG4gICAgICAgIH1cbiAgICAgICAgLnNlY3Rpb24taGVhZGVyIGgzIHtcbiAgICAgICAgICBjb2xvcjogIzExMTgyNztcbiAgICAgICAgICBmb250LXNpemU6IDI0cHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XG4gICAgICAgICAgbGV0dGVyLXNwYWNpbmc6IC0wLjAyZW07XG4gICAgICAgIH1cbiAgICAgICAgLnNlY3Rpb24taGVhZGVyIHAge1xuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTVweDtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgIH1cbiAgICAgICAgLmJlbmVmaXRzLWdyaWQge1xuICAgICAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICAgICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7XG4gICAgICAgICAgZ2FwOiAyMHB4O1xuICAgICAgICB9XG4gICAgICAgIC5iZW5lZml0LWNhcmQge1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBnYXA6IDE2cHg7XG4gICAgICAgICAgcGFkZGluZzogMjBweDtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmO1xuICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNlMWU1ZTk7XG4gICAgICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1vdXQ7XG4gICAgICAgIH1cbiAgICAgICAgLmJlbmVmaXQtY2FyZDpob3ZlciB7XG4gICAgICAgICAgYm9yZGVyLWNvbG9yOiAjZDFkNWRiO1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7XG4gICAgICAgIH1cbiAgICAgICAgLmJlbmVmaXQtaWNvbiB7XG4gICAgICAgICAgZm9udC1zaXplOiAzMnB4O1xuICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICB3aWR0aDogMzJweDtcbiAgICAgICAgICBoZWlnaHQ6IDMycHg7XG4gICAgICAgIH1cbiAgICAgICAgLmJlbmVmaXQtaWNvbiBpbWcge1xuICAgICAgICAgIG1heC13aWR0aDogMTAwJTtcbiAgICAgICAgICBtYXgtaGVpZ2h0OiAxMDAlO1xuICAgICAgICAgIG9iamVjdC1maXQ6IGNvbnRhaW47XG4gICAgICAgIH1cbiAgICAgICAgLmJlbmVmaXQtaWNvbiBzdmcge1xuICAgICAgICAgIHdpZHRoOiAyNHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgaGVpZ2h0OiAyNHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgbWF4LXdpZHRoOiAyNHB4O1xuICAgICAgICAgIG1heC1oZWlnaHQ6IDI0cHg7XG4gICAgICAgIH1cbiAgICAgICAgLmJlbmVmaXQtY29udGVudCB7XG4gICAgICAgICAgZmxleDogMTtcbiAgICAgICAgfVxuICAgICAgICAuYmVuZWZpdC1jb250ZW50IGg0IHtcbiAgICAgICAgICBjb2xvcjogIzExMTgyNztcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICBtYXJnaW46IDAgMCA0cHggMDtcbiAgICAgICAgfVxuICAgICAgICAuYmVuZWZpdC1jb250ZW50IHAge1xuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgICBsaW5lLWhlaWdodDogMS40O1xuICAgICAgICAgIG1hcmdpbjogMCAwIDhweCAwO1xuICAgICAgICB9XG4gICAgICAgIC5iZW5lZml0LWNhdGVnb3J5IHtcbiAgICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2YzZjRmNjtcbiAgICAgICAgICBjb2xvcjogIzZiNzI4MDtcbiAgICAgICAgICBmb250LXNpemU6IDEycHg7XG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICAgICAgICBwYWRkaW5nOiA0cHggOHB4O1xuICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICAgICAgICAgIGxldHRlci1zcGFjaW5nOiAwLjA1ZW07XG4gICAgICAgIH1cbiAgICAgICAgLmJlbmVmaXQtYXJyb3cge1xuICAgICAgICAgIGNvbG9yOiAjOWNhM2FmO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMThweDtcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgICAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2Utb3V0O1xuICAgICAgICB9XG4gICAgICAgIC5iZW5lZml0LWNhcmQ6aG92ZXIgLmJlbmVmaXQtYXJyb3cge1xuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg0cHgpO1xuICAgICAgICB9XG5cbiAgICAgICAgLmZvb3RlciB7XG4gICAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjtcbiAgICAgICAgICBwYWRkaW5nOiAzMnB4IDIwcHg7XG4gICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgICAgIGNvbG9yOiAjOWNhM2FmO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDtcbiAgICAgICAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2UxZTVlOTtcbiAgICAgICAgICBtYXJnaW4tdG9wOiA2NHB4O1xuICAgICAgICB9XG4gICAgICAgIC5mb290ZXIgcCB7XG4gICAgICAgICAgbWFyZ2luOiAzcHggMDtcbiAgICAgICAgfVxuICAgICAgICAuZm9vdGVyIGEge1xuICAgICAgICAgIGNvbG9yOiAjNmI3MjgwO1xuICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgICB9XG4gICAgICAgIC5mb290ZXIgYTpob3ZlciB7XG4gICAgICAgICAgY29sb3I6ICMzNzQxNTE7XG4gICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG4gICAgICAgIH1cblxuICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgICAuaGVhZGVyIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IDEycHggMTZweDtcbiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7XG4gICAgICAgICAgICBnYXA6IDEycHg7XG4gICAgICAgICAgfVxuICAgICAgICAgIC5sb2dvLXRleHQge1xuICAgICAgICAgICAgZGlzcGxheTogbm9uZTtcbiAgICAgICAgICB9XG4gICAgICAgICAgLnVzZXItZGV0YWlscyB7XG4gICAgICAgICAgICBkaXNwbGF5OiBub25lO1xuICAgICAgICAgIH1cbiAgICAgICAgICAubG9nb3V0LWJ0biB7XG4gICAgICAgICAgICBwYWRkaW5nOiA2cHggMTBweDtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgICB9XG4gICAgICAgICAgLmRhc2hib2FyZC1ncmlkIHtcbiAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgICAgICAgICAgZ2FwOiAxNnB4O1xuICAgICAgICAgIH1cbiAgICAgICAgICAuYmVuZWZpdHMtZ3JpZCB7XG4gICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgICAgICAgICAgIGdhcDogMTZweDtcbiAgICAgICAgICB9XG4gICAgICAgICAgLm1haW4tY29udGVudCB7XG4gICAgICAgICAgICBwYWRkaW5nOiAyNHB4IDE2cHg7XG4gICAgICAgICAgfVxuICAgICAgICAgIC53ZWxjb21lLXNlY3Rpb24gaDIge1xuICAgICAgICAgICAgZm9udC1zaXplOiAyOHB4O1xuICAgICAgICAgIH1cbiAgICAgICAgICAuYmVuZWZpdC1jYXJkIHtcbiAgICAgICAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICBgfTwvc3R5bGU+XG4gICAgPC8+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJIZWFkIiwicGFyc2UiLCJ1c2VTdGF0ZSIsIlJlYWN0Iiwic3R1ZGVudEJlbmVmaXRzIiwiZ2V0QmVuZWZpdHNDb3VudCIsImZldGNoR29vZ2xlVXNlciIsImVtYWlsIiwidG9rZW5SZXMiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiVVJMU2VhcmNoUGFyYW1zIiwiY2xpZW50X2lkIiwicHJvY2VzcyIsImVudiIsIkdPT0dMRV9DTElFTlRfSUQiLCJjbGllbnRfc2VjcmV0IiwiR09PR0xFX0NMSUVOVF9TRUNSRVQiLCJyZWZyZXNoX3Rva2VuIiwiR09PR0xFX1JFRlJFU0hfVE9LRU4iLCJncmFudF90eXBlIiwib2siLCJhY2Nlc3NfdG9rZW4iLCJqc29uIiwidXNlclJlcyIsImVuY29kZVVSSUNvbXBvbmVudCIsIkF1dGhvcml6YXRpb24iLCJnZXRTZXJ2ZXJTaWRlUHJvcHMiLCJyZXEiLCJjb29raWVzIiwiY29va2llIiwidXNlckVtYWlsIiwidXNlck5hbWUiLCJyZWRpcmVjdCIsImRlc3RpbmF0aW9uIiwicGVybWFuZW50IiwiYWxsb3dlZERvbWFpbiIsIkVNQUlMX0RPTUFJTiIsImRvbWFpbiIsInN0YXJ0c1dpdGgiLCJzbGljZSIsImVuZHNXaXRoIiwiZ29vZ2xlVXNlciIsImZ1bGxOYW1lIiwibmFtZSIsImdpdmVuTmFtZSIsImZhbWlseU5hbWUiLCJwZXJzb25hbEVtYWlsIiwicmVjb3ZlcnlFbWFpbCIsInN0dWRlbnRJZCIsInNwbGl0IiwicHJvcHMiLCJzdHVkZW50RW1haWwiLCJ1c2VyUGljdHVyZSIsIkljb25SZW5kZXJlciIsImljb24iLCJjbGFzc05hbWUiLCJ0eXBlIiwic3ZnRWxlbWVudCIsImNsb25lRWxlbWVudCIsImNvbnRlbnQiLCJ3aWR0aCIsImhlaWdodCIsInN0eWxlIiwiZGlzcGxheSIsImRpdiIsImltZyIsInNyYyIsImFsdCIsIm9iamVjdEZpdCIsIlN0dWRlbnRQb3J0YWwiLCJoYW5kbGVMb2dvdXQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJlcnJvciIsImNvbnNvbGUiLCJ0aXRsZSIsImhlYWRlciIsInNwYW4iLCJtYXAiLCJqb2luIiwidG9VcHBlckNhc2UiLCJidXR0b24iLCJvbkNsaWNrIiwic3ZnIiwidmlld0JveCIsImZpbGwiLCJ4bWxucyIsInBhdGgiLCJkIiwibWFpbiIsImgyIiwicCIsImgzIiwiYSIsImJlbmVmaXQiLCJsaW5rIiwidGFyZ2V0IiwicmVsIiwiaDQiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwiaWQiLCJmb290ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/student-portal.js\n");

/***/ }),

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/student-portal.js"));
module.exports = __webpack_exports__;

})();