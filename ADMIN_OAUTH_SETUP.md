# 管理员OAuth登录设置指南

## 概述
管理员面板登录方式已从密码认证改为Google OAuth认证，只有 `<EMAIL>` 邮箱可以访问管理员面板。

## Google Cloud Console 配置

### 1. 添加管理员回调URL
在Google Cloud Console中，需要将以下URL添加到OAuth 2.0客户端的授权重定向URI列表中：

**开发环境:**
```
http://localhost:3000/api/auth/google/admin-callback
```

**生产环境:**
```
https://yourdomain.com/api/auth/google/admin-callback
```

### 2. 配置步骤
1. 打开 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择您的项目
3. 导航到 `APIs & Services > Credentials`
4. 找到您的OAuth 2.0客户端ID
5. 点击编辑
6. 在"授权的重定向URI"部分添加上述URL
7. 保存更改

## 环境变量
确保以下环境变量已正确配置：

```env
# Google OAuth (用于学生登录和管理员登录)
GOOGLE_OAUTH_CLIENT_ID=your-oauth-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-oauth-client-secret
GOOGLE_OAUTH_REDIRECT_URI=http://localhost:3000/api/auth/google/callback

# 管理员配置
SESSION_SECRET=your-32-char-secret-key
```

## 新增的API端点

### 1. 管理员OAuth启动
- **路径**: `/api/auth/google/admin-initiate`
- **方法**: GET
- **功能**: 启动管理员专用的Google OAuth流程

### 2. 管理员OAuth回调
- **路径**: `/api/auth/google/admin-callback`
- **方法**: GET
- **功能**: 处理管理员OAuth回调，验证邮箱是否为 `<EMAIL>`

## 修改的文件

### 1. 管理员登录页面
- **文件**: `pages/admin/login.js`
- **变更**: 从密码登录改为Google OAuth登录

### 2. 管理员认证API
- **文件**: `pages/api/admin/auth.js`
- **变更**: 移除密码验证，改为基于OAuth session验证

### 3. 权限验证函数
- **文件**: `pages/api/admin/activation-codes.js`
- **变更**: 更新 `verifyAdmin` 函数以适配新的认证方式

## 测试流程

### 1. 访问测试页面
访问 `http://localhost:3000/test-admin-auth` 来测试认证状态

### 2. 管理员登录测试
1. 访问 `http://localhost:3000/admin/login`
2. 点击"使用 Google 登录"按钮
3. 使用 `<EMAIL>` 邮箱登录
4. 应该被重定向到管理员面板

### 3. 非管理员邮箱测试
1. 使用其他邮箱尝试登录
2. 应该显示"只有管理员邮箱 (<EMAIL>) 可以访问管理面板"错误

## 安全特性

1. **邮箱验证**: 只有 `<EMAIL>` 邮箱可以访问
2. **Session管理**: 使用安全的session token
3. **CSRF保护**: 使用state参数防止CSRF攻击
4. **Cookie安全**: 使用HttpOnly、Secure、SameSite等安全属性

## 故障排除

### 1. 回调URL错误
如果遇到"redirect_uri_mismatch"错误，请确保在Google Cloud Console中添加了正确的回调URL。

### 2. 认证失败
检查环境变量是否正确配置，特别是 `GOOGLE_OAUTH_CLIENT_ID` 和 `GOOGLE_OAUTH_CLIENT_SECRET`。

### 3. Session问题
如果遇到session相关问题，请检查 `SESSION_SECRET` 环境变量是否设置。

## 清理工作

以下环境变量不再需要，可以移除：
- `ADMIN_PASSWORD` (如果不再用于其他用途)

## 部署注意事项

1. 确保生产环境的回调URL已添加到Google Cloud Console
2. 更新生产环境的环境变量
3. 测试完整的OAuth流程
4. 确保管理员邮箱 `<EMAIL>` 存在于Google Workspace中
