"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/test-auth";
exports.ids = ["pages/test-auth"];
exports.modules = {

/***/ "./pages/test-auth.js":
/*!****************************!*\
  !*** ./pages/test-auth.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAuth),\n/* harmony export */   \"getServerSideProps\": () => (/* binding */ getServerSideProps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function getServerSideProps({ req  }) {\n    const cookies = (0,cookie__WEBPACK_IMPORTED_MODULE_3__.parse)(req.headers.cookie || \"\");\n    return {\n        props: {\n            cookies: {\n                userEmail: cookies.userEmail || null,\n                userName: cookies.userName || null,\n                userPicture: cookies.userPicture || null,\n                adminSession: cookies.adminSession || null\n            }\n        }\n    };\n}\nfunction TestAuth({ cookies  }) {\n    const handleTestLogin = ()=>{\n        window.location.href = \"/api/auth/google/initiate\";\n    };\n    const handleTestLogout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n            window.location.reload();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        }\n    };\n    const handleTestAdminLogin = ()=>{\n        window.location.href = \"/admin/login\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    className: \"jsx-8e77e54adc4f63dc\",\n                    children: \"认证系统测试 - Great Heights School\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"test-card\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-8e77e54adc4f63dc\",\n                            children: \"认证系统测试页面\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"jsx-8e77e54adc4f63dc\",\n                                    children: \"当前登录状态\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"status-info\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"status-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"用户邮箱:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + ((cookies.userEmail ? \"success\" : \"error\") || \"\"),\n                                                    children: cookies.userEmail || \"未登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"status-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"用户姓名:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + ((cookies.userName ? \"success\" : \"error\") || \"\"),\n                                                    children: cookies.userName || \"未设置\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"status-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"头像:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + ((cookies.userPicture ? \"success\" : \"error\") || \"\"),\n                                                    children: cookies.userPicture ? \"已设置\" : \"未设置\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"status-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"管理员会话:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + ((cookies.adminSession ? \"success\" : \"error\") || \"\"),\n                                                    children: cookies.adminSession ? \"已登录\" : \"未登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"jsx-8e77e54adc4f63dc\",\n                                    children: \"测试操作\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"test-buttons\",\n                                    children: [\n                                        !cookies.userEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleTestLogin,\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"btn primary\",\n                                            children: \"测试Google登录\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleTestLogout,\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"btn secondary\",\n                                            children: \"测试登出\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleTestAdminLogin,\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"btn admin\",\n                                            children: \"测试管理员登录\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/register\",\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"btn register\",\n                                            children: \"测试注册页面\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        cookies.userEmail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/student-portal\",\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"btn portal\",\n                                            children: \"进入学生门户\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"jsx-8e77e54adc4f63dc\",\n                                    children: \"系统信息\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"system-info\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"info-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"演示激活码:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"DEMO2025GHS\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"info-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"管理员密码:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"admin123\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"info-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"允许的邮箱域名:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"jsx-8e77e54adc4f63dc\",\n                                                    children: \"@ghs.edu.kg\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-8e77e54adc4f63dc\" + \" \" + \"back-link\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/\",\n                                className: \"jsx-8e77e54adc4f63dc\",\n                                children: \"← 返回首页\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Code\\\\Web\\\\GHS-EDU-SIGNUP\\\\pages\\\\test-auth.js\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"8e77e54adc4f63dc\",\n                children: \".container.jsx-8e77e54adc4f63dc{min-height:100vh;background:#f5f7fa;padding:20px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.test-card.jsx-8e77e54adc4f63dc{background:#fff;padding:40px;-webkit-border-radius:12px;-moz-border-radius:12px;border-radius:12px;-webkit-box-shadow:0 4px 12px rgba(0,0,0,.1);-moz-box-shadow:0 4px 12px rgba(0,0,0,.1);box-shadow:0 4px 12px rgba(0,0,0,.1);max-width:600px;width:100%}h1.jsx-8e77e54adc4f63dc{text-align:center;color:#333;margin-bottom:30px;font-size:24px}.section.jsx-8e77e54adc4f63dc{margin-bottom:30px;padding-bottom:20px;border-bottom:1px solid#eee}.section.jsx-8e77e54adc4f63dc:last-of-type{border-bottom:none}h2.jsx-8e77e54adc4f63dc{color:#333;font-size:18px;margin-bottom:16px}.status-info.jsx-8e77e54adc4f63dc,.system-info.jsx-8e77e54adc4f63dc{background:#f8f9fa;padding:16px;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px}.status-item.jsx-8e77e54adc4f63dc,.info-item.jsx-8e77e54adc4f63dc{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;margin-bottom:8px;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.status-item.jsx-8e77e54adc4f63dc:last-child,.info-item.jsx-8e77e54adc4f63dc:last-child{margin-bottom:0}label.jsx-8e77e54adc4f63dc{font-weight:500;color:#555}.success.jsx-8e77e54adc4f63dc{color:#28a745;font-weight:500}.error.jsx-8e77e54adc4f63dc{color:#dc3545;font-weight:500}code.jsx-8e77e54adc4f63dc{background:#e9ecef;padding:2px 6px;-webkit-border-radius:3px;-moz-border-radius:3px;border-radius:3px;font-family:monospace;font-size:14px}.test-buttons.jsx-8e77e54adc4f63dc{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:12px}.btn.jsx-8e77e54adc4f63dc{padding:12px 20px;border:none;-webkit-border-radius:6px;-moz-border-radius:6px;border-radius:6px;cursor:pointer;font-size:14px;font-weight:500;text-decoration:none;text-align:center;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease}.btn.primary.jsx-8e77e54adc4f63dc{background:#4285f4;color:#fff}.btn.primary.jsx-8e77e54adc4f63dc:hover{background:#3367d6}.btn.secondary.jsx-8e77e54adc4f63dc{background:#6c757d;color:#fff}.btn.secondary.jsx-8e77e54adc4f63dc:hover{background:#545b62}.btn.admin.jsx-8e77e54adc4f63dc{background:#17a2b8;color:#fff}.btn.admin.jsx-8e77e54adc4f63dc:hover{background:#138496}.btn.register.jsx-8e77e54adc4f63dc{background:#28a745;color:#fff}.btn.register.jsx-8e77e54adc4f63dc:hover{background:#218838}.btn.portal.jsx-8e77e54adc4f63dc{background:#fd7e14;color:#fff}.btn.portal.jsx-8e77e54adc4f63dc:hover{background:#e8650e}.back-link.jsx-8e77e54adc4f63dc{text-align:center;margin-top:20px}.back-link.jsx-8e77e54adc4f63dc a.jsx-8e77e54adc4f63dc{color:#666;text-decoration:none;font-size:14px}.back-link.jsx-8e77e54adc4f63dc a.jsx-8e77e54adc4f63dc:hover{color:#4285f4;text-decoration:underline}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/test-auth.js\n");

/***/ }),

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next/head");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/test-auth.js"));
module.exports = __webpack_exports__;

})();