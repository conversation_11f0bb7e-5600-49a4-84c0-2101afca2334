import Head from 'next/head'
import { parse } from 'cookie'
import { useState } from 'react'
import React from 'react'
import { studentBenefits, getBenefitsCount } from '../data/student-benefits'

async function fetchGoogleUser(email) {
  // 刷新 token
  const tokenRes = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
      client_id:     process.env.GOOGLE_CLIENT_ID,
      client_secret: process.env.GOOGLE_CLIENT_SECRET,
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
      grant_type:    'refresh_token',
    }),
  })
  if (!tokenRes.ok) return null
  const { access_token } = await tokenRes.json()
  
  // 查询 Directory
  const userRes = await fetch(
    `https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(email)}`,
    { headers: { Authorization: `Bearer ${access_token}` } }
  )
  if (!userRes.ok) return null
  return await userRes.json()
}

export async function getServerSideProps({ req }) {
  const cookies = parse(req.headers.cookie || '')
  const userEmail = cookies.userEmail
  const userName = cookies.userName

  // 检查是否已登录（通过Google OAuth）
  if (!userEmail || !userName) {
    return { redirect: { destination: '/login', permanent: false } }
  }

  // 验证邮箱域名
  const allowedDomain = process.env.EMAIL_DOMAIN || 'ghs.edu.kg'
  const domain = allowedDomain.startsWith('@') ? allowedDomain.slice(1) : allowedDomain
  
  if (!userEmail.endsWith(`@${domain}`)) {
    return { redirect: { destination: '/login?error=invalid_domain', permanent: false } }
  }

  // 获取Google Directory中的用户信息
  const googleUser = await fetchGoogleUser(userEmail)
  if (!googleUser) {
    return { redirect: { destination: '/login?error=user_not_found', permanent: false } }
  }

  // 从Google Directory获取用户信息
  const fullName = `${googleUser.name.givenName} ${googleUser.name.familyName}`
  const personalEmail = googleUser.recoveryEmail || ''
  // 从邮箱中提取用户名作为学生ID
  const studentId = userEmail.split('@')[0]

  return {
    props: {
      fullName,
      studentEmail: userEmail,
      personalEmail,
      studentId,
      userPicture: cookies.userPicture || ''
    }
  }
}

// 图标渲染组件
const IconRenderer = ({ icon, className = '' }) => {
  if (icon.type === 'svg') {
    // 克隆SVG元素并强制设置统一尺寸
    const svgElement = React.cloneElement(icon.content, {
      width: '24',
      height: '24',
      style: { width: '24px', height: '24px', display: 'block' }
    })
    return <div className={className}>{svgElement}</div>
  } else if (icon.type === 'image') {
    return (
      <div className={className}>
        <img
          src={icon.content}
          alt={icon.alt || '图标'}
          style={{ width: '24px', height: '24px', objectFit: 'contain' }}
        />
      </div>
    )
  }
  return null
}

export default function StudentPortal({ fullName, studentEmail, personalEmail, studentId, userPicture }) {
  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      window.location.href = '/'
    } catch (error) {
      console.error('Logout error:', error)
      // 即使出错也跳转到首页
      window.location.href = '/'
    }
  }



  return (
    <>
      <Head>
        <title>学生门户 - Great Heights School</title>
      </Head>
      <div className="container">
        <header className="header">
          <div className="header-left">
            <div className="logo">
              <img src="/resources/logo.jpg" alt="Great Heights School Logo" className="logo-image" />
              <div className="logo-text">
                <span className="school-name">Great Heights School</span>
                <span className="portal-name">Student Portal</span>
              </div>
            </div>
          </div>
          <div className="header-right">
            <div className="user-info">
              <div className="user-avatar">
                {userPicture ? (
                  <img src={userPicture} alt="Profile" className="avatar-image" />
                ) : (
                  <div className="avatar-placeholder">
                    {fullName.split(' ').map(name => name[0]).join('').toUpperCase()}
                  </div>
                )}
              </div>
              <div className="user-details">
                <span className="user-name">{fullName}</span>
                <span className="user-email">{studentEmail}</span>
              </div>
              <button onClick={handleLogout} className="logout-btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z" fill="currentColor"/>
                  <path d="M4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z" fill="currentColor"/>
                </svg>
                登出
              </button>
            </div>
          </div>
        </header>

        <main className="main-content">
          {/* 欢迎区域 */}
          <div className="welcome-section">
            <h2>欢迎回来, {fullName}!</h2>
            <p>探索您的学生权益，充分利用教育资源</p>
          </div>

          {/* 仪表盘网格布局 */}
          <div className="dashboard-grid">
            {/* 学生信息卡片 */}
            <div className="dashboard-card student-info-card">
              <div className="card-header">
                <h3>学生信息</h3>
                <div className="card-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="currentColor"/>
                  </svg>
                </div>
              </div>
              <div className="card-content">
                <div className="info-item">
                  <span className="label">学生ID</span>
                  <span className="value">{studentId}</span>
                </div>
                <div className="info-item">
                  <span className="label">姓名</span>
                  <span className="value">{fullName}</span>
                </div>
                <div className="info-item">
                  <span className="label">学校邮箱</span>
                  <span className="value">{studentEmail}</span>
                </div>
                <div className="info-item">
                  <span className="label">个人邮箱</span>
                  <span className="value">{personalEmail || '未设置'}</span>
                </div>
              </div>
            </div>

            {/* 快速操作卡片 */}
            <div className="dashboard-card quick-actions-card">
              <div className="card-header">
                <h3>快速操作</h3>
                <div className="card-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 2v11h3v9l7-12h-4l4-8z" fill="currentColor"/>
                  </svg>
                </div>
              </div>
              <div className="card-content">
                <a href="/reset-password" className="action-button">
                  <div className="action-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z" fill="currentColor"/>
                    </svg>
                  </div>
                  <div className="action-text">
                    <span className="action-title">重置密码</span>
                    <span className="action-desc">修改您的账户密码</span>
                  </div>
                </a>
              </div>
            </div>

            {/* 学生权益展示区域 */}
            <div className="dashboard-card benefits-overview-card">
              <div className="card-header">
                <h3>学生权益概览</h3>
                <div className="card-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z" fill="currentColor"/>
                  </svg>
                </div>
              </div>
              <div className="card-content">
                <div className="benefits-stats">
                  <div className="stat-item">
                    <span className="stat-number">{getBenefitsCount()}</span>
                    <span className="stat-label">可用权益</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">∞</span>
                    <span className="stat-label">节省金额</span>
                  </div>
                </div>
                <p className="benefits-desc">
                  作为 Great Heights School 的学生，您可以享受多项专属权益和优惠服务
                </p>
              </div>
            </div>
          </div>

          {/* 学生权益详细展示 */}
          <div className="benefits-section">
            <div className="section-header">
              <h3>学生专享权益</h3>
              <p>点击卡片了解更多详情并访问服务</p>
            </div>
            <div className="benefits-grid">
              {studentBenefits.map((benefit) => (
                <a
                  key={benefit.id}
                  href={benefit.link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="benefit-card"
                >
                  <IconRenderer icon={benefit.icon} className="benefit-icon" />
                  <div className="benefit-content">
                    <h4>{benefit.name}</h4>
                    <p>{benefit.description}</p>
                    <span className="benefit-category">{benefit.category}</span>
                  </div>
                  <div className="benefit-arrow">→</div>
                </a>
              ))}
            </div>
          </div>
        </main>

        <footer className="footer">
          <p>
            Powered by{' '}
            <a href="#" target="_blank" rel="noopener">
              Garbage Human Studio
            </a>
          </p>
          <p>© 2025 Great Heights School. All rights reserved.</p>
        </footer>
      </div>

      <style jsx>{`
        .container {
          min-height: 100vh;
          background: #fafafa;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        .header {
          background: #ffffff;
          padding: 16px 32px;
          border-bottom: 1px solid #e1e5e9;
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: sticky;
          top: 0;
          z-index: 100;
        }
        .header-left {
          display: flex;
          align-items: center;
        }
        .header-right {
          display: flex;
          align-items: center;
        }
        .logo {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        .logo-image {
          width: 32px;
          height: 32px;
          object-fit: contain;
          border-radius: 4px;
        }
        .logo-text {
          display: flex;
          flex-direction: column;
        }
        .school-name {
          color: #111827;
          font-size: 16px;
          font-weight: 600;
          letter-spacing: -0.02em;
          line-height: 1.2;
        }
        .portal-name {
          color: #6b7280;
          font-size: 13px;
          font-weight: 400;
          line-height: 1.2;
        }
        .user-info {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          overflow: hidden;
          flex-shrink: 0;
        }
        .avatar-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: #111827;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
        }
        .user-details {
          display: flex;
          flex-direction: column;
          min-width: 0;
        }
        .user-name {
          font-weight: 600;
          color: #111827;
          font-size: 14px;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .user-email {
          font-size: 12px;
          color: #6b7280;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .logout-btn {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          background: #ffffff;
          color: #6b7280;
          border: 1px solid #e1e5e9;
          border-radius: 6px;
          cursor: pointer;
          font-size: 13px;
          font-weight: 500;
          transition: all 0.15s ease-out;
          white-space: nowrap;
        }
        .logout-btn:hover {
          background: #f9fafb;
          color: #374151;
          border-color: #d1d5db;
        }
        .main-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 32px 20px;
        }
        .welcome-section {
          text-align: center;
          margin-bottom: 40px;
        }
        .welcome-section h2 {
          color: #111827;
          font-size: 32px;
          font-weight: 600;
          margin-bottom: 8px;
          letter-spacing: -0.02em;
        }
        .welcome-section p {
          color: #6b7280;
          font-size: 16px;
          margin: 0;
        }
        /* 仪表盘网格布局 */
        .dashboard-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 24px;
          margin-bottom: 48px;
        }
        .dashboard-card {
          background: #ffffff;
          border: 1px solid #e1e5e9;
          border-radius: 12px;
          padding: 24px;
          transition: all 0.2s ease-out;
        }
        .dashboard-card:hover {
          border-color: #d1d5db;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }
        .card-header h3 {
          color: #111827;
          font-size: 18px;
          font-weight: 600;
          margin: 0;
        }
        .card-icon {
          font-size: 24px;
          opacity: 0.7;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .card-icon svg {
          width: 24px !important;
          height: 24px !important;
          max-width: 24px;
          max-height: 24px;
        }
        .card-content {
          color: #374151;
        }

        /* 学生信息卡片 */
        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f3f4f6;
        }
        .info-item:last-child {
          border-bottom: none;
        }
        .info-item .label {
          font-size: 14px;
          color: #6b7280;
          font-weight: 500;
        }
        .info-item .value {
          font-size: 14px;
          color: #111827;
          font-weight: 500;
        }

        /* 快速操作卡片 */
        .action-button {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          background: #f9fafb;
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          text-decoration: none;
          transition: all 0.15s ease-out;
        }
        .action-button:hover {
          background: #f3f4f6;
          border-color: #d1d5db;
          transform: translateY(-1px);
        }
        .action-icon {
          font-size: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .action-icon svg {
          width: 20px !important;
          height: 20px !important;
          max-width: 20px;
          max-height: 20px;
        }
        .action-text {
          display: flex;
          flex-direction: column;
        }
        .action-title {
          color: #111827;
          font-weight: 600;
          font-size: 14px;
        }
        .action-desc {
          color: #6b7280;
          font-size: 13px;
        }

        /* 权益概览卡片 */
        .benefits-stats {
          display: flex;
          gap: 24px;
          margin-bottom: 16px;
        }
        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .stat-number {
          font-size: 24px;
          font-weight: 700;
          color: #111827;
        }
        .stat-label {
          font-size: 12px;
          color: #6b7280;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        .benefits-desc {
          color: #6b7280;
          font-size: 14px;
          line-height: 1.5;
          margin: 0;
        }
        /* 学生权益展示区域 */
        .benefits-section {
          margin-top: 48px;
        }
        .section-header {
          text-align: center;
          margin-bottom: 32px;
        }
        .section-header h3 {
          color: #111827;
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 8px;
          letter-spacing: -0.02em;
        }
        .section-header p {
          color: #6b7280;
          font-size: 15px;
          margin: 0;
        }
        .benefits-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }
        .benefit-card {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 20px;
          background: #ffffff;
          border: 1px solid #e1e5e9;
          border-radius: 12px;
          text-decoration: none;
          transition: all 0.2s ease-out;
        }
        .benefit-card:hover {
          border-color: #d1d5db;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .benefit-icon {
          font-size: 32px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
        }
        .benefit-icon img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
        .benefit-icon svg {
          width: 24px !important;
          height: 24px !important;
          max-width: 24px;
          max-height: 24px;
        }
        .benefit-content {
          flex: 1;
        }
        .benefit-content h4 {
          color: #111827;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 4px 0;
        }
        .benefit-content p {
          color: #6b7280;
          font-size: 14px;
          line-height: 1.4;
          margin: 0 0 8px 0;
        }
        .benefit-category {
          display: inline-block;
          background: #f3f4f6;
          color: #6b7280;
          font-size: 12px;
          font-weight: 500;
          padding: 4px 8px;
          border-radius: 4px;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        .benefit-arrow {
          color: #9ca3af;
          font-size: 18px;
          font-weight: 600;
          flex-shrink: 0;
          transition: all 0.2s ease-out;
        }
        .benefit-card:hover .benefit-arrow {
          color: #6b7280;
          transform: translateX(4px);
        }

        .footer {
          background: #ffffff;
          padding: 32px 20px;
          text-align: center;
          color: #9ca3af;
          font-size: 13px;
          border-top: 1px solid #e1e5e9;
          margin-top: 64px;
        }
        .footer p {
          margin: 3px 0;
        }
        .footer a {
          color: #6b7280;
          text-decoration: none;
          font-weight: 500;
        }
        .footer a:hover {
          color: #374151;
          text-decoration: underline;
        }

        @media (max-width: 768px) {
          .header {
            padding: 12px 16px;
            flex-direction: row;
            gap: 12px;
          }
          .logo-text {
            display: none;
          }
          .user-details {
            display: none;
          }
          .logout-btn {
            padding: 6px 10px;
            font-size: 12px;
          }
          .dashboard-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }
          .benefits-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }
          .main-content {
            padding: 24px 16px;
          }
          .welcome-section h2 {
            font-size: 28px;
          }
          .benefit-card {
            padding: 16px;
          }
        }
      `}</style>
    </>
  )
}
