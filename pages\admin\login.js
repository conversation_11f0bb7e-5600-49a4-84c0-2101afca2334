import Head from 'next/head'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'

export default function AdminLogin() {
  const router = useRouter()
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // 处理URL中的错误参数
    if (router.query.error) {
      switch (router.query.error) {
        case 'oauth_error':
          setError('Google登录过程中出现错误，请重试')
          break
        case 'invalid_state':
          setError('登录验证失败，请重试')
          break
        case 'no_code':
          setError('未收到授权码，请重试')
          break
        case 'token_error':
          setError('获取访问令牌失败，请重试')
          break
        case 'user_info_error':
          setError('获取用户信息失败，请重试')
          break
        case 'unauthorized':
          setError('只有管理员邮箱 (<EMAIL>) 可以访问管理面板')
          break
        case 'server_error':
          setError('服务器错误，请稍后重试')
          break
        default:
          setError('登录失败，请重试')
      }
    }
  }, [router.query])

  const handleGoogleLogin = () => {
    setIsLoading(true)
    setError('')
    window.location.href = '/api/auth/google/admin-initiate'
  }

  return (
    <>
      <Head>
        <title>管理员登录 - Great Heights School</title>
      </Head>
      <div className="container">
        <div className="login-card">
          <div className="header">
            <div className="logo">
              <img src="/resources/logo.jpg" alt="Great Heights School Logo" className="logo-image" />
              <div className="title-section">
                <h1>管理员登录</h1>
                <p className="subtitle">Great Heights School 激活码管理系统</p>
              </div>
            </div>
          </div>

          {error && (
            <div className="error-message">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
              </svg>
              {error}
            </div>
          )}

          <div className="login-section">
            <p className="login-instruction">
              请使用管理员邮箱登录
            </p>

            <button
              onClick={handleGoogleLogin}
              disabled={isLoading}
              className="google-login-btn"
            >
              {isLoading ? (
                <span className="loading">
                  <span className="spinner"></span>
                  登录中...
                </span>
              ) : (
                <span className="google-login-content">
                  <svg className="google-icon" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                  使用 Google 登录
                </span>
              )}
            </button>
          </div>

          <div className="back-link">
            <a href="/">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
              </svg>
              返回首页
            </a>
          </div>
        </div>

        <footer>
          <p>
            <span className="powered-by">
              <svg className="icon" width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
              Powered by{' '}
              <a href="https://www.ghs.red/" target="_blank" rel="noopener">
                Garbage Human Studio
              </a>
            </span>
          </p>
          <p className="copyright">
            © 2025 Great Heights School. All rights reserved.
          </p>
        </footer>
      </div>

      <style jsx>{`
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #fafafa;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        .login-card {
          background: #ffffff;
          max-width: 480px;
          width: 100%;
          padding: 40px;
          border-radius: 12px;
          border: 1px solid #e1e5e9;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .header {
          margin-bottom: 32px;
        }
        .logo {
          display: flex;
          align-items: center;
          gap: 16px;
          justify-content: center;
          margin-bottom: 24px;
        }
        .logo-image {
          width: 48px;
          height: 48px;
          object-fit: contain;
          border-radius: 4px;
        }
        .title-section {
          text-align: center;
        }
        .title-section h1 {
          color: #111827;
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          letter-spacing: -0.02em;
        }
        .subtitle {
          color: #6b7280;
          margin: 4px 0 0 0;
          font-size: 15px;
          font-weight: 400;
        }
        .error-message {
          background: #fef2f2;
          color: #dc2626;
          padding: 12px 16px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 1px solid #fecaca;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .login-section {
          margin-bottom: 24px;
        }
        .login-instruction {
          color: #6b7280;
          margin-bottom: 24px;
          font-size: 15px;
          text-align: center;
          line-height: 1.5;
        }
        .google-login-btn {
          width: 100%;
          padding: 12px 16px;
          background: #ffffff;
          color: #374151;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          font-size: 15px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.15s ease-out;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        .google-login-btn:hover:not(:disabled) {
          background: #f9fafb;
          border-color: #9ca3af;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .google-login-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
        .google-login-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        .google-icon {
          width: 20px;
          height: 20px;
          flex-shrink: 0;
        }
        .loading {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .spinner {
          width: 14px;
          height: 14px;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #ffffff;
          border-radius: 50%;
          animation: spin 0.8s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .back-link {
          text-align: center;
          margin-top: 24px;
        }
        .back-link a {
          color: #6b7280;
          text-decoration: none;
          font-size: 14px;
          font-weight: 500;
          display: inline-flex;
          align-items: center;
          gap: 6px;
          transition: all 0.15s ease-out;
        }
        .back-link a:hover {
          color: #111827;
        }
        footer {
          margin-top: 32px;
          text-align: center;
          color: #9ca3af;
          font-size: 13px;
        }
        footer p {
          margin: 3px 0;
        }
        .powered-by {
          display: inline-flex;
          align-items: center;
          gap: 4px;
        }
        .icon {
          flex-shrink: 0;
        }
        footer a {
          color: #6b7280;
          text-decoration: none;
          font-weight: 500;
        }
        footer a:hover {
          color: #374151;
          text-decoration: underline;
        }
        @media (max-width: 640px) {
          .container {
            padding: 16px;
          }
          .login-card {
            padding: 24px;
          }
          .logo {
            flex-direction: column;
            gap: 8px;
          }
          .logo-image {
            width: 40px;
            height: 40px;
          }
          .title-section h1 {
            font-size: 20px;
          }
        }
      `}</style>
    </>
  )
}
