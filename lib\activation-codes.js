// lib/activation-codes.js
import fs from 'fs'
import path from 'path'
import { v4 as uuidv4 } from 'uuid'

const CODES_FILE = path.join(process.cwd(), 'data', 'activation-codes.json')

// 确保数据目录存在
function ensureDataDir() {
  const dataDir = path.dirname(CODES_FILE)
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true })
  }
}

// 读取激活码数据
function readCodesData() {
  ensureDataDir()
  if (!fs.existsSync(CODES_FILE)) {
    return []
  }
  try {
    const data = fs.readFileSync(CODES_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading activation codes:', error)
    return []
  }
}

// 写入激活码数据
function writeCodesData(codes) {
  ensureDataDir()
  try {
    fs.writeFileSync(CODES_FILE, JSON.stringify(codes, null, 2))
    return true
  } catch (error) {
    console.error('Error writing activation codes:', error)
    return false
  }
}

// 生成激活码
export function generateActivationCode(description = '', expiresInDays = 30, maxUsageCount = 1) {
  const codes = readCodesData()
  const code = uuidv4().replace(/-/g, '').substring(0, 16).toUpperCase()
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + expiresInDays)

  const newCode = {
    id: uuidv4(),
    code,
    description,
    createdAt: new Date().toISOString(),
    expiresAt: expiresAt.toISOString(),
    maxUsageCount: Math.max(1, parseInt(maxUsageCount) || 1), // 确保至少为1
    usedCount: 0,
    isUsed: false, // 保持向后兼容，当 usedCount >= maxUsageCount 时为 true
    usedAt: null,
    usedBy: null, // 对于多次使用的激活码，这将存储最后一次使用者
    usedByList: [], // 新增：存储所有使用者的列表
    isActive: true
  }

  codes.push(newCode)

  if (writeCodesData(codes)) {
    return newCode
  }
  return null
}

// 验证激活码
export function validateActivationCode(code) {
  const codes = readCodesData()
  const activationCode = codes.find(c => c.code === code.toUpperCase())

  if (!activationCode) {
    return { valid: false, error: 'INVALID_CODE' }
  }

  if (!activationCode.isActive) {
    return { valid: false, error: 'DISABLED_CODE' }
  }

  // 检查使用次数限制
  const usedCount = activationCode.usedCount || 0
  const maxUsageCount = activationCode.maxUsageCount || 1

  if (usedCount >= maxUsageCount) {
    return { valid: false, error: 'USAGE_LIMIT_EXCEEDED' }
  }

  // 保持向后兼容性检查
  if (activationCode.isUsed && maxUsageCount === 1) {
    return { valid: false, error: 'USED_CODE' }
  }

  if (new Date() > new Date(activationCode.expiresAt)) {
    return { valid: false, error: 'EXPIRED_CODE' }
  }

  return { valid: true, code: activationCode }
}

// 使用激活码
export function useActivationCode(code, userEmail) {
  const codes = readCodesData()
  const codeIndex = codes.findIndex(c => c.code === code.toUpperCase())

  if (codeIndex === -1) {
    return false
  }

  const validation = validateActivationCode(code)
  if (!validation.valid) {
    return false
  }

  const activationCode = codes[codeIndex]

  // 增加使用次数
  activationCode.usedCount = (activationCode.usedCount || 0) + 1

  // 更新使用者列表
  if (!activationCode.usedByList) {
    activationCode.usedByList = []
  }
  activationCode.usedByList.push({
    email: userEmail,
    usedAt: new Date().toISOString()
  })

  // 更新最后使用信息（保持向后兼容）
  activationCode.usedAt = new Date().toISOString()
  activationCode.usedBy = userEmail

  // 如果达到最大使用次数，标记为已使用（保持向后兼容）
  const maxUsageCount = activationCode.maxUsageCount || 1
  if (activationCode.usedCount >= maxUsageCount) {
    activationCode.isUsed = true
  }

  return writeCodesData(codes)
}

// 获取所有激活码
export function getAllActivationCodes() {
  return readCodesData()
}

// 禁用激活码
export function disableActivationCode(codeId) {
  const codes = readCodesData()
  const codeIndex = codes.findIndex(c => c.id === codeId)
  
  if (codeIndex === -1) {
    return false
  }
  
  codes[codeIndex].isActive = false
  return writeCodesData(codes)
}

// 启用激活码
export function enableActivationCode(codeId) {
  const codes = readCodesData()
  const codeIndex = codes.findIndex(c => c.id === codeId)
  
  if (codeIndex === -1) {
    return false
  }
  
  codes[codeIndex].isActive = true
  return writeCodesData(codes)
}

// 删除激活码
export function deleteActivationCode(codeId) {
  const codes = readCodesData()
  const filteredCodes = codes.filter(c => c.id !== codeId)
  
  if (filteredCodes.length === codes.length) {
    return false // 没有找到要删除的代码
  }
  
  return writeCodesData(filteredCodes)
}

// 获取所有使用者列表
export function getAllUsers() {
  const codes = readCodesData()
  const users = []

  codes.forEach(code => {
    if (code.usedByList && code.usedByList.length > 0) {
      code.usedByList.forEach(usage => {
        users.push({
          email: usage.email,
          usedAt: usage.usedAt,
          activationCode: code.code,
          codeDescription: code.description || '无描述',
          codeId: code.id,
          maxUsageCount: code.maxUsageCount || 1,
          currentUsedCount: code.usedCount || 0
        })
      })
    }
  })

  // 按使用时间倒序排列
  users.sort((a, b) => new Date(b.usedAt) - new Date(a.usedAt))

  return users
}

// 获取激活码统计信息
export function getActivationCodeStats() {
  const codes = readCodesData()
  const total = codes.length
  const active = codes.filter(c => c.isActive).length
  const expired = codes.filter(c => new Date() > new Date(c.expiresAt)).length

  // 重新计算使用状态，考虑使用次数
  const used = codes.filter(c => {
    const usedCount = c.usedCount || 0
    const maxUsageCount = c.maxUsageCount || 1
    return usedCount >= maxUsageCount
  }).length

  const available = codes.filter(c => {
    const usedCount = c.usedCount || 0
    const maxUsageCount = c.maxUsageCount || 1
    return c.isActive &&
           usedCount < maxUsageCount &&
           new Date() <= new Date(c.expiresAt)
  }).length

  // 新增统计：多次使用激活码相关
  const multiUse = codes.filter(c => (c.maxUsageCount || 1) > 1).length
  const totalUsages = codes.reduce((sum, c) => sum + (c.usedCount || 0), 0)
  const partiallyUsed = codes.filter(c => {
    const usedCount = c.usedCount || 0
    const maxUsageCount = c.maxUsageCount || 1
    return usedCount > 0 && usedCount < maxUsageCount
  }).length

  return {
    total,
    active,
    used,
    expired,
    available,
    multiUse,
    totalUsages,
    partiallyUsed
  }
}
