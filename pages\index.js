import Head from 'next/head'
import { useRouter } from 'next/router'

export default function Home() {
  const router = useRouter()

  return (
    <>
      <Head>
        <title>Great Heights School - 学生注册系统</title>
        <meta name="description" content="Great Heights School 学生注册和登录系统" />
      </Head>
      <div className="container">
        <div className="welcome-card">
          <div className="logo-section">
            <div className="logo-placeholder">
              <img src="/resources/logo.jpg" alt="Great Heights School Logo" className="logo-image" />
            </div>
            <h1>Great Heights School</h1>
            <p className="subtitle">学生注册与登录系统</p>
          </div>

          <div className="action-section">
            <div className="action-card">
              <h3>新学生注册</h3>
              <p>使用激活码创建您的学校账号</p>
              <a href="/register" className="btn btn-primary">
                立即注册
              </a>
            </div>

            <div className="action-card">
              <h3>学生登录</h3>
              <p>使用您的学校邮箱登录系统</p>
              <a href="/login" className="btn btn-secondary">
                登录账号
              </a>
            </div>
          </div>
        </div>

        <footer>
          <p>
            <span className="powered-by">
              <svg className="icon" width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
              Powered by{' '}
              <a href="https://www.ghs.red/" target="_blank" rel="noopener">
                Garbage Human Studio
              </a>
            </span>
          </p>
          <p className="copyright">
            © 2025 Great Heights School. All rights reserved.
          </p>
        </footer>
      </div>
      <style jsx>{`
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #fafafa;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        .welcome-card {
          background: #ffffff;
          padding: 48px 40px;
          border-radius: 12px;
          border: 1px solid #e1e5e9;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          text-align: center;
          max-width: 800px;
          width: 100%;
        }
        .logo-section {
          margin-bottom: 32px;
        }
        .logo-placeholder {
          width: 64px;
          height: 64px;
          border-radius: 12px;
          margin: 0 auto 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        }
        .logo-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        .logo-section h1 {
          color: #111827;
          margin: 0 0 6px 0;
          font-size: 28px;
          font-weight: 600;
          letter-spacing: -0.02em;
        }
        .subtitle {
          color: #6b7280;
          margin: 0;
          font-size: 15px;
          font-weight: 400;
        }
        .action-section {
          display: flex;
          gap: 24px;
          justify-content: center;
          margin-bottom: 0;
        }
        .action-card {
          background: #ffffff;
          padding: 24px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
          transition: all 0.2s ease-out;
          flex: 1;
          max-width: 280px;
        }
        .action-card:hover {
          border-color: #d1d5db;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        .action-card h3 {
          color: #111827;
          margin-bottom: 6px;
          font-size: 18px;
          font-weight: 600;
        }
        .action-card p {
          color: #6b7280;
          margin-bottom: 20px;
          font-size: 14px;
          line-height: 1.5;
        }
        .btn {
          display: inline-block;
          padding: 10px 20px;
          border-radius: 6px;
          text-decoration: none;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.15s ease-out;
          border: 1px solid transparent;
        }
        .btn-primary {
          background: #111827;
          color: #ffffff;
          border-color: #111827;
        }
        .btn-primary:hover {
          background: #374151;
          border-color: #374151;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .btn-secondary {
          background: #ffffff;
          color: #374151;
          border-color: #d1d5db;
        }
        .btn-secondary:hover {
          background: #f9fafb;
          color: #111827;
          border-color: #9ca3af;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        footer {
          margin-top: 32px;
          text-align: center;
          color: #9ca3af;
          font-size: 13px;
        }
        footer p {
          margin: 3px 0;
        }
        .powered-by {
          display: inline-flex;
          align-items: center;
          gap: 4px;
        }
        .icon {
          flex-shrink: 0;
        }
        footer a {
          color: #6b7280;
          text-decoration: none;
          font-weight: 500;
          margin-left: 4px;
        }
        footer a:hover {
          color: #374151;
          text-decoration: underline;
        }
        .copyright {
          font-size: 12px;
          color: #9ca3af;
          display: inline-flex;
          align-items: center;
          gap: 4px;
        }
        @media (max-width: 1023px) {
          .welcome-card {
            max-width: 480px;
          }
          .action-section {
            flex-direction: column;
            gap: 12px;
          }
          .action-card {
            max-width: none;
            padding: 20px;
          }
        }
        @media (max-width: 480px) {
          .container {
            padding: 16px;
          }
          .welcome-card {
            padding: 32px 24px;
            max-width: none;
          }
          .logo-section h1 {
            font-size: 24px;
          }
          .subtitle {
            font-size: 14px;
          }
          .action-card {
            padding: 16px;
          }
          .action-card h3 {
            font-size: 16px;
          }
          .btn {
            padding: 10px 16px;
            font-size: 13px;
          }
        }
      `}</style>
    </>
  )
}
