"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/auth";
exports.ids = ["pages/api/admin/auth"];
exports.modules = {

/***/ "cookie":
/*!*************************!*\
  !*** external "cookie" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("cookie");

/***/ }),

/***/ "(api)/./pages/api/admin/auth.js":
/*!*********************************!*\
  !*** ./pages/api/admin/auth.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"cookie\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(cookie__WEBPACK_IMPORTED_MODULE_0__);\n// pages/api/admin/auth.js\n\nconst SESSION_SECRET = process.env.SESSION_SECRET || \"your-secret-key\";\nconst ADMIN_EMAIL = \"<EMAIL>\";\nfunction handler(req, res) {\n    if (req.method === \"GET\") {\n        // 验证管理员session\n        const cookies = cookie__WEBPACK_IMPORTED_MODULE_0___default().parse(req.headers.cookie || \"\");\n        const sessionToken = cookies.adminSession;\n        const adminEmail = cookies.adminEmail;\n        if (!sessionToken || !adminEmail) {\n            return res.status(401).json({\n                authenticated: false\n            });\n        }\n        // 验证邮箱是否为管理员邮箱\n        if (adminEmail !== ADMIN_EMAIL) {\n            return res.status(401).json({\n                authenticated: false\n            });\n        }\n        try {\n            const decoded = Buffer.from(sessionToken, \"base64\").toString();\n            const [user, timestamp, secret] = decoded.split(\":\");\n            if (user !== \"admin\" || secret !== SESSION_SECRET) {\n                return res.status(401).json({\n                    authenticated: false\n                });\n            }\n            // 检查session是否过期（24小时）\n            const sessionTime = parseInt(timestamp);\n            const now = Date.now();\n            const maxAge = 24 * 60 * 60 * 1000 // 24小时\n            ;\n            if (now - sessionTime > maxAge) {\n                return res.status(401).json({\n                    authenticated: false,\n                    message: \"Session过期\"\n                });\n            }\n            res.status(200).json({\n                authenticated: true,\n                email: adminEmail\n            });\n        } catch (error) {\n            res.status(401).json({\n                authenticated: false\n            });\n        }\n    } else if (req.method === \"DELETE\") {\n        // 登出 - 清除所有管理员相关的cookies\n        const clearCookies = [\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"adminSession\", \"\", {\n                path: \"/\",\n                expires: new Date(0)\n            }),\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"adminEmail\", \"\", {\n                path: \"/\",\n                expires: new Date(0)\n            }),\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"adminName\", \"\", {\n                path: \"/\",\n                expires: new Date(0)\n            }),\n            cookie__WEBPACK_IMPORTED_MODULE_0___default().serialize(\"adminPicture\", \"\", {\n                path: \"/\",\n                expires: new Date(0)\n            })\n        ];\n        res.setHeader(\"Set-Cookie\", clearCookies);\n        res.status(200).json({\n            success: true,\n            message: \"已登出\"\n        });\n    } else {\n        res.setHeader(\"Allow\", [\n            \"GET\",\n            \"DELETE\"\n        ]);\n        res.status(405).end(`Method ${req.method} Not Allowed`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/auth.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/admin/auth.js"));
module.exports = __webpack_exports__;

})();