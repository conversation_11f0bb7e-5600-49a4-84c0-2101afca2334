// pages/reset-password.js
import Head from 'next/head'
import { parse } from 'cookie'
import { useState } from 'react'

async function fetchGoogleToken(retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const res = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          client_id: process.env.GOOGLE_CLIENT_ID,
          client_secret: process.env.GOOGLE_CLIENT_SECRET,
          refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
          grant_type: 'refresh_token'
        }),
        timeout: 10000 // 10秒超时
      })

      if (!res.ok) {
        console.error(`Google token fetch failed: ${res.status} ${res.statusText}`)
        return null
      }

      const { access_token } = await res.json()
      return access_token
    } catch (error) {
      console.error(`Google token fetch attempt ${i + 1} failed:`, error.message)

      // 如果是最后一次重试，返回null
      if (i === retries - 1) {
        return null
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
  return null
}

export async function getServerSideProps({ req }) {
  const cookies = parse(req.headers.cookie || '')
  const userEmail = cookies.userEmail
  const userName = cookies.userName

  // 检查是否已登录（通过Google OAuth）
  if (!userEmail || !userName) {
    return { redirect: { destination: '/login', permanent: false } }
  }

  // 验证邮箱域名
  const allowedDomain = process.env.EMAIL_DOMAIN || 'ghs.edu.kg'
  const domain = allowedDomain.startsWith('@') ? allowedDomain.slice(1) : allowedDomain

  if (!userEmail.endsWith(`@${domain}`)) {
    return { redirect: { destination: '/login?error=invalid_domain', permanent: false } }
  }

  // 确保用户存在于Google Directory
  try {
    const token = await fetchGoogleToken()
    let exists = false

    if (token) {
      try {
        const res = await fetch(
          `https://admin.googleapis.com/admin/directory/v1/users/${encodeURIComponent(userEmail)}`,
          {
            headers: { Authorization: `Bearer ${token}` },
            timeout: 10000 // 10秒超时
          }
        )
        exists = res.ok
      } catch (directoryError) {
        console.error('Google Directory API error:', directoryError.message)
        // 如果Directory API失败，我们仍然允许用户访问重置密码页面
        // 因为用户已经通过OAuth验证了身份
        exists = true
      }
    } else {
      console.error('Failed to get Google token, but allowing access since user is authenticated')
      // 如果无法获取token，但用户已经通过OAuth验证，仍然允许访问
      exists = true
    }

    if (!exists) {
      return { redirect: { destination: '/login?error=user_not_found', permanent: false } }
    }

    return { props: { studentEmail: userEmail } }
  } catch (error) {
    console.error('Error in getServerSideProps:', error.message)
    // 如果出现任何错误，但用户已经通过OAuth验证，仍然允许访问
    return { props: { studentEmail: userEmail } }
  }
}

export default function ResetPassword({ studentEmail }) {
  const [pwd, setPwd] = useState('')
  const [confirm, setConfirm] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const handleSubmit = async e => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    if (!pwd) {
      setError('请输入新密码')
      setIsLoading(false)
      return
    }
    if (pwd.length < 8) {
      setError('密码长度至少需要8位')
      setIsLoading(false)
      return
    }
    if (pwd !== confirm) {
      setError('两次输入的密码不一致')
      setIsLoading(false)
      return
    }

    try {
      const res = await fetch('/api/reset-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password: pwd })
      })

      if (res.ok) {
        setSuccess(true)
        setTimeout(() => {
          window.location.href = '/student-portal'
        }, 2000)
      } else {
        const text = await res.text()
        setError(`重置失败: ${text}`)
      }
    } catch (error) {
      setError('网络错误，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>重置密码 - Great Heights School</title>
      </Head>
      <div className="container">
        <div className="card">
          <div className="header">
            <div className="logo">
              <svg width="32" height="32" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="45" fill="#111827" stroke="#fff" strokeWidth="2"/>
                <text x="50" y="58" textAnchor="middle" fill="#fff" fontSize="24" fontWeight="bold">
                  GHS
                </text>
              </svg>
              <div className="title-section">
                <h1>重置密码</h1>
                <p className="subtitle">Great Heights School</p>
              </div>
            </div>
          </div>

          <div className="account-info">
            <div className="info-label">当前账户</div>
            <div className="info-value">{studentEmail}</div>
          </div>

          {error && (
            <div className="error-message">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
              </svg>
              {error}
            </div>
          )}

          {success && (
            <div className="success-message">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>
              </svg>
              密码重置成功！正在跳转到学生门户...
            </div>
          )}

          <form onSubmit={handleSubmit} className="form">
            <div className="form-group">
              <label htmlFor="password">新密码</label>
              <input
                type="password"
                id="password"
                value={pwd}
                onChange={e => setPwd(e.target.value)}
                placeholder="请输入新密码（至少8位）"
                required
                disabled={isLoading || success}
              />
            </div>

            <div className="form-group">
              <label htmlFor="confirm">确认密码</label>
              <input
                type="password"
                id="confirm"
                value={confirm}
                onChange={e => setConfirm(e.target.value)}
                placeholder="请再次输入新密码"
                required
                disabled={isLoading || success}
              />
            </div>

            <button
              type="submit"
              className="submit-btn"
              disabled={isLoading || success}
            >
              {isLoading ? (
                <span className="loading">
                  <span className="spinner"></span>
                  重置中...
                </span>
              ) : success ? (
                '重置成功'
              ) : (
                '重置密码'
              )}
            </button>
          </form>

          <div className="back-link">
            <a href="/student-portal">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z" fill="currentColor"/>
              </svg>
              返回学生门户
            </a>
          </div>
        </div>

        <footer>
          <p>
            <span className="powered-by">
              <svg className="icon" width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
              Powered by{' '}
              <a href="https://www.ghs.red/" target="_blank" rel="noopener">
                Garbage Human Studio
              </a>
            </span>
          </p>
          <p className="copyright">
            <svg className="icon" width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            © 2025 Great Heights School. All rights reserved.
          </p>
        </footer>
      </div>
      <style jsx>{`
        .container {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: #fafafa;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        }
        .card {
          background: #ffffff;
          max-width: 480px;
          width: 100%;
          padding: 40px;
          border-radius: 12px;
          border: 1px solid #e1e5e9;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .header {
          margin-bottom: 32px;
        }
        .logo {
          display: flex;
          align-items: center;
          gap: 12px;
          justify-content: center;
        }
        .title-section {
          text-align: center;
        }
        .title-section h1 {
          color: #111827;
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          letter-spacing: -0.02em;
        }
        .subtitle {
          color: #6b7280;
          margin: 4px 0 0 0;
          font-size: 15px;
          font-weight: 400;
        }
        .account-info {
          background: #f9fafb;
          padding: 16px;
          border-radius: 8px;
          margin-bottom: 24px;
          border: 1px solid #e5e7eb;
        }
        .info-label {
          color: #6b7280;
          font-size: 13px;
          font-weight: 500;
          margin-bottom: 4px;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        .info-value {
          color: #111827;
          font-size: 15px;
          font-weight: 600;
        }
        .error-message {
          background: #fef2f2;
          color: #dc2626;
          padding: 12px 16px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 1px solid #fecaca;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .success-message {
          background: #f0fdf4;
          color: #16a34a;
          padding: 12px 16px;
          border-radius: 8px;
          margin-bottom: 20px;
          border: 1px solid #bbf7d0;
          font-size: 14px;
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .form {
          margin-bottom: 24px;
        }
        .form-group {
          margin-bottom: 20px;
        }
        .form-group label {
          display: block;
          color: #374151;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 6px;
        }
        .form-group input {
          width: 100%;
          padding: 12px 16px;
          border: 1px solid #d1d5db;
          border-radius: 8px;
          font-size: 15px;
          transition: all 0.15s ease-out;
          box-sizing: border-box;
        }
        .form-group input:focus {
          outline: none;
          border-color: #111827;
          box-shadow: 0 0 0 3px rgba(17, 24, 39, 0.1);
        }
        .form-group input:disabled {
          background: #f9fafb;
          color: #9ca3af;
          cursor: not-allowed;
        }
        .form-group input::placeholder {
          color: #9ca3af;
        }
        .submit-btn {
          width: 100%;
          padding: 12px 16px;
          background: #111827;
          color: #ffffff;
          border: none;
          border-radius: 8px;
          font-size: 15px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.15s ease-out;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
        }
        .submit-btn:hover:not(:disabled) {
          background: #374151;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .submit-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }
        .loading {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .spinner {
          width: 14px;
          height: 14px;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #ffffff;
          border-radius: 50%;
          animation: spin 0.8s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        .back-link {
          text-align: center;
          margin-top: 24px;
        }
        .back-link a {
          color: #6b7280;
          text-decoration: none;
          font-size: 14px;
          font-weight: 500;
          display: inline-flex;
          align-items: center;
          gap: 6px;
          transition: all 0.15s ease-out;
        }
        .back-link a:hover {
          color: #111827;
        }
        footer {
          margin-top: 32px;
          text-align: center;
          color: #9ca3af;
          font-size: 13px;
        }
        footer p {
          margin: 3px 0;
        }
        .powered-by {
          display: inline-flex;
          align-items: center;
          gap: 4px;
        }
        .icon {
          flex-shrink: 0;
        }
        footer a {
          color: #6b7280;
          text-decoration: none;
          font-weight: 500;
        }
        footer a:hover {
          color: #374151;
          text-decoration: underline;
        }
        @media (max-width: 640px) {
          .container {
            padding: 16px;
          }
          .card {
            padding: 24px;
          }
          .logo {
            flex-direction: column;
            gap: 8px;
          }
          .title-section h1 {
            font-size: 20px;
          }
        }
      `}</style>
    </>
  )
}
