// pages/api/admin/users.js
import { getAllUsers } from '../../../lib/activation-codes'
import cookie from 'cookie'

const SESSION_SECRET = process.env.SESSION_SECRET || 'your-secret-key'
const ADMIN_EMAIL = '<EMAIL>'

// 配置API路由
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}

// 验证管理员身份
function verifyAdminAuth(req) {
  const cookies = cookie.parse(req.headers.cookie || '')
  const sessionCookie = cookies.admin_session

  if (!sessionCookie) {
    return false
  }

  try {
    // 简单的session验证 - 在生产环境中应该使用更安全的方法
    const sessionData = JSON.parse(Buffer.from(sessionCookie, 'base64').toString())
    
    // 检查session是否过期（24小时）
    const sessionAge = Date.now() - sessionData.timestamp
    const maxAge = 24 * 60 * 60 * 1000 // 24小时

    if (sessionAge > maxAge) {
      return false
    }

    // 验证邮箱
    return sessionData.email === ADMIN_EMAIL
  } catch (error) {
    return false
  }
}

export default async function handler(req, res) {
  const { method } = req

  // 验证管理员权限
  if (!verifyAdminAuth(req)) {
    return res.status(401).json({ error: 'Unauthorized' })
  }

  switch (method) {
    case 'GET':
      // 获取所有用户列表
      try {
        const users = getAllUsers()
        res.status(200).json({ users })
      } catch (error) {
        console.error('Error fetching users:', error)
        res.status(500).json({ error: 'Failed to fetch users' })
      }
      break

    default:
      res.setHeader('Allow', ['GET'])
      res.status(405).end(`Method ${method} Not Allowed`)
  }
}
